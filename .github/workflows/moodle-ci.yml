name: <PERSON><PERSON><PERSON> Plugin CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-20.04

    services:
      postgres:
        image: postgres:13.0
        env:
          POSTGRES_USER: 'postgres'
          POSTGRES_HOST_AUTH_METHOD: 'trust'
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 3
      mariadb:
        image: mariadb:10.6.7
        env:
          MYSQL_USER: 'root'
          MYSQL_ALLOW_EMPTY_PASSWORD: "true"
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval 10s --health-timeout 5s --health-retries 3

    strategy:
      fail-fast: false
      matrix:
        include:
          - php: '8.2'
            moodle-branch: 'MOODLE_403_STABLE'
            database: 'mariadb'
          - php: '8.1'
            moodle-branch: 'MOODLE_403_STABLE'
            database: 'pgsql'
          - php: '8.0'
            moodle-branch: 'MOODLE_403_STABLE'
            database: 'mariadb'
          - php: '8.1'
            moodle-branch: 'MOODLE_402_STABLE'
            database: 'pgsql'
          - php: '8.0'
            moodle-branch: 'MOODLE_402_STABLE'
            database: 'mariadb'
          - php: '8.0'
            moodle-branch: 'MOODLE_401_STABLE'
            database: 'pgsql'
          - php: '8.0'
            moodle-branch: 'MOODLE_400_STABLE'
            database: 'pgsql'
          - php: '7.4'
            moodle-branch: 'MOODLE_400_STABLE'
            database: 'pgsql'

    steps:
      - name: Check out repository code
        uses: actions/checkout@v2
        with:
          path: plugin

      - name: Setup PHP ${{ matrix.php }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: max_input_vars=6000
          coverage: none

      - name: Initialise moodle-plugin-ci
        run: |
          composer create-project -n --no-dev --prefer-dist moodlehq/moodle-plugin-ci ci ^3
          echo $(cd ci/bin; pwd) >> $GITHUB_PATH
          echo $(cd ci/vendor/bin; pwd) >> $GITHUB_PATH
          sudo locale-gen en_AU.UTF-8

      - name: Install moodle-plugin-ci
        run: |
          moodle-plugin-ci install --plugin ./plugin --db-host=127.0.0.1
        env:
          DB: ${{ matrix.database }}
          MOODLE_BRANCH: ${{ matrix.moodle-branch }}

      - name: PHP Lint
        if: ${{ always() }}
        run: moodle-plugin-ci phplint

      - name: PHP Copy/Paste Detector
        if: ${{ always() }}
        run: moodle-plugin-ci phpcpd

      - name: PHP Mess Detector
        if: ${{ always() }}
        run: moodle-plugin-ci phpmd

      - name: Moodle Code Checker
        if: ${{ always() }}
        run: moodle-plugin-ci codechecker --max-warnings 0

      - name: Moodle PHPDoc Checker
        if: ${{ always() }}
        run: moodle-plugin-ci phpdoc

      - name: Validating
        if: ${{ always() }}
        run: moodle-plugin-ci validate

      - name: Check upgrade savepoints
        if: ${{ always() }}
        run: moodle-plugin-ci savepoints

      - name: Mustache Lint
        if: ${{ always() }}
        run: moodle-plugin-ci mustache

      - name: Grunt
        if: ${{ always() }}
        run: moodle-plugin-ci grunt

      - name: PHPUnit tests
        if: ${{ always() }}
        run: moodle-plugin-ci phpunit

      - name: Behat features
        if: ${{ always() }}
        run: moodle-plugin-ci behat --profile chrome
