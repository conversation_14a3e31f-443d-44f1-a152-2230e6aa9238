CONTRIBUTING TO MOODLE
======================

Moodle is made by people like you. We are members of a big worldwide community
of developers, designers, teachers, testers, translators and other users. We
work in universities, schools, companies and other places. You are very welcome
to join us and contribute to the project.

See <https://moodledev.io/general/community/contribute> for the many ways you
can help, not only with coding.

Moodle is open to community contributions to core, though all code must go
through peer-review, automated behaviour testing, continuous integration and
human post-integration checks.

Pull requests
-------------

Please do not open pull requests via Github. The repository there is just a
mirror of the official repository at <https://git.moodle.org>. Issues are
reported and patches provided via <https://tracker.moodle.org>. See below for
more information.

Moodle core bug fixes and new features
--------------------------------------

During the years of intensive development, a mature process of including
submitted patches has evolved.

* Every bug fix or new feature must have a tracker issue.
* You publish the branch implementing the fix or new feature in your public
  clone of the moodle.git repository (typically on Github).
* Your patch is peer-reviewed, discussed, integrated, tested and then released
  as a part of moodle.git.
* New features are developed on the master branch. Bug fixes are also
  backported to currently supported maintenance (stable) branches.

For further details, see <https://moodledev.io/general/development/process>.

Moodle plugins
--------------

Moodle has a framework for additional plugins to extend its functionality. We
have a Moodle plugins directory <https://moodle.org/plugins/> where you can
register and maintain your plugin. Plugins hosted in the plugins directory can
be easily installed and updated via the Moodle administration interface.

* You are expected to have a public source code repository with your plugin
  code.
* After registering your plugin in the plugins directory it is reviewed before
  being published.
* You are expected to continuously release updated versions of the plugin via
  the plugins directory. We do not pull from your code repository; you must do
  it explicitly.

For further details, see <https://moodledev.io/general/community/plugincontribution>.
