{"name": "<PERSON><PERSON><PERSON>", "private": true, "description": "<PERSON><PERSON><PERSON>", "devDependencies": {"@babel/core": "7.17.5", "@babel/eslint-parser": "^7.21.3", "@babel/eslint-plugin": "7.19.1", "@babel/preset-env": "7.16.11", "@xmldom/xmldom": "^0.8.7", "ajv": "^8.12.0", "async": "^3.2.5", "babel-plugin-system-import-transformer": "^4.0.0", "babel-plugin-transform-es2015-modules-amd-lazy": "2.0.1", "babel-preset-minify": "0.5.1", "cross-env": "^7.0.3", "docdash": "^2.0.2", "eslint": "^8.56.0", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-promise": "6.0.0", "fb-watchman": "^2.0.2", "gherkin-lint": "^4.2.2", "glob": "^10.3.10", "grunt": "^1.6.1", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "24.0.0", "grunt-rollup": "^11.9.0", "grunt-sass": "3.1.0", "grunt-stylelint": "^0.19.0", "hugo-bin": "^0.80.2", "hugo-lunr-indexer": "^1.1.3", "jsdoc": "^4.0.2", "jsdoc-to-markdown": "^8.0.0", "jshint": "^2.13.4", "jstoxml": "^3.2.3", "npm-run-all": "^4.1.5", "postcss-scss": "^4.0.9", "rollup-plugin-terser": "^7.0.2", "sass": "^1.58.3", "semver": "7.3.5", "shifter": "https://github.com/andrewnicols/shifter/archive/v1.2.0_moodle-0.tar.gz", "stylelint": "^15.11.0", "stylelint-csstree-validator": "^3.0.0", "xpath": "0.0.32"}, "engines": {"node": ">=20.11.0 <21"}, "browserslist": [">0.3%", "last 2 versions", "not ie >= 0", "not op_mini all", "not Opera > 0", "not dead"]}