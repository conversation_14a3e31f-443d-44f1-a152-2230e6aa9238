<?xml version="1.0" encoding="UTF-8"?>
<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../lib/phpunit/phpunit.xsd"
        bootstrap="../../../lib/phpunit/bootstrap.php"
        convertErrorsToExceptions="true"
        convertNoticesToExceptions="true"
        convertWarningsToExceptions="true"
        processIsolation="false"
        backupGlobals="false"
        backupStaticAttributes="false"
        cacheResult="false"
        stopOnError="false"
        stopOnFailure="false"
        stopOnIncomplete="false"
        stopOnSkipped="false"
        beStrictAboutTestsThatDoNotTestAnything="false"
        beStrictAboutOutputDuringTests="true"
        testSuiteLoaderClass="phpunit_autoloader"
        >

    <php>
        <!--<const name="PHPUNIT_LONGTEST" value="1"/> uncomment to execute also slow or otherwise expensive tests-->
        <const name="PHPUNIT_SEQUENCE_START" value="163000"/>

        <!--Following constants instruct tests to fetch external test files from alternative location or skip tests if empty, clone https://github.com/moodlehq/moodle-exttests to local web server-->
        <!--<const name="TEST_EXTERNAL_FILES_HTTP_URL" value="http://download.moodle.org/unittest"/> uncomment and alter to fetch external test files from alternative location-->
        <!--<const name="TEST_EXTERNAL_FILES_HTTPS_URL" value="https://download.moodle.org/unittest"/> uncomment and alter to fetch external test files from alternative location-->
    </php>


    <testsuites>
        <testsuite name="availability_sectioncompleted_testsuite">
            <directory suffix="_test.php">.</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="false">
            <directory suffix=".php">.</directory>
            <exclude>
                <directory suffix="_test.php">.</directory>
                <file>version.php</file>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
