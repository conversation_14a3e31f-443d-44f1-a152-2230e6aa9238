<?php
defined('MOODLE_INTERNAL') || die();
$finder = PhpCsFixer\Finder::create()
	->in(__DIR__);

return PhpCsFixer\Config::create()
	->setRules([
		'@PSR2' => true,
		'phpdoc_separation' => true,
		'phpdoc_wrap' => true,
		'lowercase_constants' => true,
		'elseif' => true,
		'single_line_after_imports' => true,
		'space_before_closure_parenthesis' => false,
		'line_ending' => "\n",
		'max_line_length' => 180,
		'no_trailing_whitespace' => true,
		'no_blank_lines_before_namespace' => true,
		'no_blank_lines_after_phpdoc' => true,
		'no_extra_blank_lines' => [
			'tokens' => [
				'extra',
				'throw',
				'use',
				'use_trait',
				'curly_brace_block',
				'parenthesis_brace_block',
				'square_brace_block',
				'return',
				'continue',
				'break',
				'throw',
				'switch',
				'case',
				'default',
			],
		],
		'braces' => [
			'position_after_functions_and_oop_constructs' => 'next',
			'position_after_control_structures' => 'next',
			'position_after_anonymous_constructs' => 'next',
		],
		'align_multiline_comment' => false,
		'no_empty_comment' => false,
		'blank_line_before_statement' => [
			'statements' => ['return'],
		],
		'class_definition' => [
			'single_line' => true,
		],
		'method_argument_space' => [
			'on_multiline' => 'ensure_fully_multiline',
			'keep_multiple_spaces_after_comma' => false,
		],
		'cast_spaces' => ['space' => 'single'],
		'method_chaining_indentation' => true,
		'binary_operator_spaces' => [
			'default' => 'single_space',
			'operators' => ['=>' => 'align_single_space_minimal'],
		],
		'ternary_operator_spaces' => true,
		'for_spaces' => true,
		'array_syntax' => ['syntax' => 'short'],
		'assignment_operator_spaces' => true,
		'control_structure_braces' => true,
		'indentation_type' => true,
		'indent_size' => 8,
	])
	->setFinder($finder);
