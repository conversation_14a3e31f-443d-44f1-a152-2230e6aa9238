<?php

use local_iauthorise\cicompetency;
use local_iauthorise\management\company\tables\requirements_table;
use local_iauthorise\management\company\tables\requirements_table_filterset;

require_once '../../../../config.php';
require_once($CFG->libdir . '/adminlib.php');

$id = required_param('id', PARAM_INT);
$delete = optional_param('delete', 0, PARAM_INT);
$confirm = optional_param('confirm', '', PARAM_ALPHANUM);

$context = context_system::instance();
require_login();
require_capability('local/iauthorise:manage_icompetencies', $context);

$cicompetency = new cicompetency($id);

$page_url = new moodle_url('/local/iauthorise/management/company/cicompetency_details.php', ['id' => $id]);
$manageurl = new moodle_url('/local/iauthorise/management/company/manage_cicompetencies.php');

$PAGE->set_context($context);
$PAGE->set_url($page_url);
$PAGE->set_pagelayout('admin');
$PAGE->set_title($cicompetency->fullname);
$PAGE->set_heading($cicompetency->fullname);
$PAGE->set_secondary_navigation(false);

$PAGE->navbar->add(get_string('manage_cicompetency', 'local_iauthorise'), $manageurl);
$PAGE->navbar->add($cicompetency->fullname);

$PAGE->requires->js_call_amd('local_iauthorise/company/cicomp_requirement', 'init', [$context->id]);

if ($delete && confirm_sesskey()) {
    if ($confirm != md5($delete)) {
        echo $OUTPUT->header();
        echo $OUTPUT->heading(get_string('cicomp_req_delete', 'local_iauthorise'));
        $optionsyes = array('delete' => $delete, 'confirm' => md5($delete), 'sesskey' => sesskey());
        $deleteurl = new moodle_url($page_url, $optionsyes);
        $deletebutton = new single_button($deleteurl, get_string('delete'), 'post');
        echo $OUTPUT->confirm(get_string('confirm_cicomp_req_delete', 'local_iauthorise'), $deletebutton, $page_url);
        echo $OUTPUT->footer();
        die;
    } else if (data_submitted()) {
        $deleted = $cicompetency->remove_requirement($delete);
        $msg = get_string('cicomp_req_deleted', 'local_iauthorise');
        \core\notification::success($msg);
        redirect($page_url);
    }
}

$requirements_table = new requirements_table('cicomp-requirements-table', $page_url, $cicompetency);
$filterset = new requirements_table_filterset();
$requirements_table->set_filterset($filterset);

echo $OUTPUT->header();

// Display if requirements added or not
$requirements = $cicompetency->get_requirements();
if (!empty($requirements)) {
    echo $OUTPUT->heading(get_string('configure_cicomp_comp_requirement_for', 'local_iauthorise', $cicompetency->fullname), 5);
}


echo html_writer::start_tag('div', ['class' => 'container cicomp-requirements-add-container mt-3 d-flex justify-content-end']);
echo html_writer::start_tag('div', ['class' => 'row']);
if (has_capability('local/iauthorise:update_icompetency', $context)) {
    echo html_writer::tag('button', get_string('add_requirement', 'local_iauthorise'),
        [
            'type' => 'button',
            'class' => 'btn btn-secondary',
            'data-action' => 'cicomp-req-add',
            'data-cicomp-id' => $id
        ]);
}

echo html_writer::end_tag('div');
echo html_writer::end_tag('div');


echo html_writer::start_tag('div', ['class' => 'container mt-5', 'data-region' => "cicomp-req-table"]);
$requirements_table->out(10, false);
echo html_writer::end_tag('div');

echo $OUTPUT->footer();