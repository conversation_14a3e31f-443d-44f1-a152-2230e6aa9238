#!/bin/bash

# Define the directories
SOURCE_DIR="/opt/homebrew/var/www/enphunew/language_files_collection"
COMPARE_DIR="/opt/homebrew/var/language_files_collection2"

# Check if both directories exist
if [ ! -d "$SOURCE_DIR" ] || [ ! -d "$COMPARE_DIR" ]; then
    echo "Error: One or both directories do not exist"
    exit 1
fi

# Counter for removed files
removed_count=0

# Process each file in the second directory
for file in "$COMPARE_DIR"/*.php; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")

        # Check if the file exists in the source directory
        if [ ! -f "$SOURCE_DIR/$filename" ]; then
            echo "Removing: $filename"
            rm "$file"
            ((removed_count++))
        fi
    fi
done

# Print summary
echo -e "\nOperation completed!"
echo "$removed_count files were removed from $COMPARE_DIR"
echo -e "\nRemaining files in $COMPARE_DIR:"
ls -1 "$COMPARE_DIR"/*.php 2>/dev/null || echo "No PHP files remaining"
