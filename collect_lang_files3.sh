#!/bin/bash

# Define the base directory (where the script is run from)
BASE_DIR=$(pwd)

# Define the destination directory for language files
DEST_DIR="/opt/homebrew/var/language_files_collection2"

# Create the destination directory if it doesn't exist
if [ ! -d "$DEST_DIR" ]; then
    mkdir -p "$DEST_DIR"
    echo "Created directory: $DEST_DIR"
fi

# Function to process a directory for language files
process_directory() {
    local dir="$1"
    local indent="$2"

    # Check if lang/en directory exists and copy files
    if [ -d "$dir/lang/en" ]; then
        echo "${indent}Processing language files from: $dir"

        # Copy all PHP files from the lang/en directory
        for file in "$dir/lang/en/"*.php; do
            if [ -f "$file" ]; then
                cp "$file" "$DEST_DIR/"
                echo "${indent}✓ Copied: $(basename $file)"
            fi
        done
    fi

    # Check for .gitmodules in this directory
    if [ -f "$dir/.gitmodules" ]; then
        # Process each submodule in this directory
        while IFS= read -r line; do
            if [[ $line =~ path[[:space:]]*=[[:space:]]*(.+)$ ]]; then
                local SUBMODULE_PATH="${BASH_REMATCH[1]}"
                local FULL_PATH="$dir/$SUBMODULE_PATH"

                if [ -d "$FULL_PATH" ]; then
                    echo "${indent}Found submodule: $SUBMODULE_PATH"
                    # Recursively process this submodule
                    process_directory "$FULL_PATH" "$indent  "
                fi
            fi
        done < "$dir/.gitmodules"
    fi
}

# Start processing from the base directory
echo "Starting language files collection..."
process_directory "$BASE_DIR" ""

echo -e "\nLanguage files collection completed!"
echo "Files are stored in: $DEST_DIR"

# List all collected files
echo -e "\nCollected language files:"
find "$DEST_DIR" -type f -name "*.php" | while read -r file; do
    echo "- $(basename $file)"
done

# Print total count of files
total_files=$(find "$DEST_DIR" -type f -name "*.php" | wc -l)
echo -e "\nTotal number of language files collected: $total_files"
