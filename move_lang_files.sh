#!/bin/bash

# Define the destination directory
DEST_DIR="/opt/homebrew/var/language_files_collection"

# Check if the collection directory exists
if [ ! -d "$DEST_DIR" ]; then
    echo "Language collection directory not found!"
    exit 1
fi

echo "Moving files to main directory..."

# Find all PHP files in subdirectories and move them to main directory
# Adding prefix of subdirectory name to avoid filename conflicts
find "$DEST_DIR" -mindepth 2 -type f -name "*.php" | while read -r file; do
    # Get subdirectory name
    subdir=$(basename "$(dirname "$file")")
    # Get filename
    filename=$(basename "$file")
    # Create new filename with subdirectory prefix
    new_filename="${subdir}_${filename}"

    # Move file to main directory with new name
    mv "$file" "$DEST_DIR/$new_filename"
    echo "✓ Moved: $filename -> $new_filename"
done

# Remove empty subdirectories
find "$DEST_DIR" -mindepth 1 -type d -empty -delete

echo -e "\nFiles moved successfully!"
echo "All language files are now in: $DEST_DIR"

# List all files in the main directory
echo -e "\nLanguage files in main directory:"
ls -1 "$DEST_DIR"/*.php 2>/dev/null
