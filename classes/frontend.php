<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>odle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Front-end class.
 *
 * @package availability_prerequisitecompleted
 * @copyright MU DOT MY PLT <<EMAIL>>
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace availability_prerequisitecompleted;

defined('MOODLE_INTERNAL') || die();

class frontend extends \core_availability\frontend {
    /**
     * @var array Cached init parameters
     */
    protected $cacheparams = array();

    /**
     * @var string IDs of course, cm, and section for cache (if any)
     */
    protected $cachekey = '';

    protected function get_javascript_strings() {
        return array('option_complete', 'label_cm', 'label_completion');
    }

    protected function get_javascript_init_params($course, \cm_info $cm = null,
        \section_info $section = null) {
        // Use cached result if available. The cache is just because we call it
        // twice (once from allow_add) so it's nice to avoid doing all the
        // print_string calls twice.
        $cachekey = $course->id . ',' . ($cm ? $cm->id : '') . ($section ? $section->id : '');
        if ($cachekey !== $this->cachekey) {
            // Get list of activities on course which have completion values,
            // to fill the dropdown.
            $context = \context_course::instance($course->id);
            //get all course name
            $datalps = array();
            global $DB;
            $sql2 = "SELECT lp.id,lp.learning_plan FROM {learning_learningplan} lp
            JOIN {lp_sets} lps on lps.lpid = lp.id
            JOIN {lp_set_items} lpsi on lps.id = lpsi.lpset_id  AND lpsi.course_id = ? ";
            $lps = $DB->get_records_sql($sql2, [$course->id]);

            foreach ($lps as $lp) {
                //disable not created course and default course

                $datalps[] = (object)array(
                    'id' => $lp->id,
                    'name' => format_string($lp->learning_plan, true, array('context' => $context))
                );

            }
            $this->cachekey = $cachekey;
            $this->cacheinitparams = array($datalps);
        }
        return $this->cacheinitparams;
    }

    protected function allow_add($course, \cm_info $cm = null,
        \section_info $section = null) {
        global $CFG;

        // Check if completion is enabled for the course.
        require_once($CFG->libdir . '/completionlib.php');
        $info = new \completion_info($course);
        if (!$info->is_enabled()) {
            return false;
        }

        // Check if there's at least one other module with completion info.
        $params = $this->get_javascript_init_params($course, $cm, $section);
        return ((array)$params[0]) != false;
    }
}
