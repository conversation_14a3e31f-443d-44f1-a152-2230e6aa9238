<?php

namespace local_iauthorise\management\company\forms;

use context_system;
use core_form\dynamic_form;
use dml_exception;
use invalid_parameter_exception;
use local_iauthorise\cicompetency;
use moodle_url;
use required_capability_exception;

defined('MOODLE_INTERNAL') || die;

/**
 * Form for adding requirements to Company Installation Competencies.
 *
 * @package    local_iauthorise
 * @copyright  2024 Enphase
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class requirements_form extends dynamic_form {

    /**
     * Form definition.
     */
    public function definition() {
        global $DB;
        $mform = $this->_form;

        $cicomp_id = $this->get_cicomp_id();
        $cicompetency = new cicompetency($cicomp_id);
        $existing_requirements = array_keys($cicompetency->get_requirements());

        // Get all user competencies that are not already requirements
        $sql = "SELECT id, fullname 
            FROM {icomp} 
            WHERE id NOT IN (" . implode(',', array_merge($existing_requirements, [0])) . ")";
        $icomps = $DB->get_records_sql_menu($sql);

        $mform->addElement('select', 'icomp_id', get_string('icompetency', 'local_iauthorise'), $icomps);
        $mform->addRule('icomp_id', null, 'required');

        $mform->addElement('hidden', 'cicomp_id');
        $mform->setType('cicomp_id', PARAM_INT);
        $mform->setDefault('cicomp_id', $cicomp_id);
    }

    private function get_cicomp_id(): int {
        $cicomp_id = $this->optional_param('cicomp_id', 0, PARAM_INT);
        if (!$cicomp_id){
            throw new invalid_parameter_exception('Company Installation Competency ID is required');
        }
        return $cicomp_id;
    }

    /**
     * Process the form submission
     *
     * @throws dml_exception
     */
    public function process_dynamic_submission(): void {
        $data = $this->get_data();
        $cicompetency = new cicompetency($data->cicomp_id);
        $cicompetency->add_requirement($data->icomp_id);
    }

    /**
     * Load in existing data as form defaults
     */
    public function set_data_for_dynamic_submission(): void {
        $cicomp_id = $this->get_cicomp_id();
        $this->set_data(['cicomp_id' => $cicomp_id]);
    }

    /**
     * Check if current user has access to this form, otherwise throw exception
     *
     * @throws required_capability_exception|dml_exception
     */
    protected function check_access_for_dynamic_submission(): void {
        require_capability('local/iauthorise:update_icompetency', context_system::instance());
    }

    /**
     * Returns form context
     *
     * @throws dml_exception
     */
    protected function get_context_for_dynamic_submission(): \context {
        return context_system::instance();
    }

    /**
     * Returns url to set as $PAGE->set_url() when form is being rendered or submitted via AJAX
     */
    protected function get_page_url_for_dynamic_submission(): moodle_url {
        $cicomp_id = $this->get_cicomp_id();
        return new moodle_url('/local/iauthorise/management/company/cicompetency_details.php', ['id' => $cicomp_id]);
    }
}