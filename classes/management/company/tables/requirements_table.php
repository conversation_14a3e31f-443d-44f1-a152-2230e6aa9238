<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  requirements_table.php description here.
 *
 * @package
 * @copyright  2024 kmp <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_iauthorise\management\company\tables;
defined('MOODLE_INTERNAL') || die;

use action_menu;
use action_menu_link;
use context;
use context_system;
use dml_exception;
use lang_string;
use local_iauthorise\cicompetency;
use moodle_exception;
use moodle_url;
use pix_icon;
use table_sql;
use core_table\dynamic as dynamic_table;

require_once $CFG->libdir . "/tablelib.php";

class requirements_table extends table_sql implements dynamic_table{

    private static string $manage_page_url = '/local/iauthorise/management/company/cicompetency_details.php';
    private static string $delete_page_url = '/local/iauthorise/management/company/cicompetency_details.php';

    private int $cicomp_id;

    function __construct($uniqueid, $baseurl, cicompetency $cicompetency, array $searchparams = []) {

        parent::__construct($uniqueid);

        $cicomp_id = $cicompetency->id;
        $this->cicomp_id = $cicomp_id;

        $this->define_baseurl($baseurl);

        $head_cols = [
            'icomp_name' => get_string('icomp_name', 'local_iauthorise'),
            'actions' => '',
        ];

        $this->define_columns(array_keys($head_cols));
        $this->define_headers(array_values($head_cols));

        // Allow pagination.
        $this->pageable(true);

        $this->sortable(false);

        list($fields, $from, $where, $where_params) = $this->get_sql_fragments($cicomp_id, $searchparams);

        list($count_sql, $count_where_params) = $this->count_sql($cicomp_id, $searchparams);

        $this->set_sql($fields, $from, $where, $where_params);

        $this->set_count_sql($count_sql, $count_where_params);
    }

    /**
     * Generates the SQL fragments for the table query.
     *
     * @param int $cicomp_id The ID of the cicomp to generate the fragments for.
     * @param array $searchparams The search parameters to filter the results by.
     *
     * @return array The SQL fragments as an array with the following structure:
     *      [
     *          string $fields The fields to select from the database.
     *          string $from The FROM clause of the query.
     *          string $where The WHERE clause of the query.
     *          array $whereparams The parameters for the WHERE clause.
     *      ]
     */
    private function get_sql_fragments($cicomp_id, array $searchparams = []): array {

        $sql = "SELECT cicr.id,
                    ic.id AS icomp_id,
                    ic.fullname AS icomp_name 
                FROM {cicomp_requirements} cicr
                JOIN {icomp} ic ON ic.id = cicr.icomp_id";
        $fields = 'cicr.id,
          ic.id AS icomp_id,
          ic.fullname AS icomp_name';
        $from = '{cicomp_requirements} cicr
                JOIN {icomp} ic ON ic.id = cicr.icomp_id';
        $where = '1=1 AND cicr.cicomp_id = :cicomp_id';
        $whereparams = [];
        $whereparams['cicomp_id'] = $cicomp_id;
        return [$fields, $from, $where, $whereparams];
    }

    /**
     * Generates the SQL fragments for the count query.
     *
     * @param int $cicomp_id The ID of the cicomp to generate the fragments for.
     * @param array $searchparams The search parameters to filter the results by.
     *
     * @return array The SQL fragments as an array with the following structure:
     *      [
     *          string $sql The SQL query to execute to get the count.
     *          array $whereparams The parameters for the WHERE clause.
     *      ]
     */
    private function count_sql($cicomp_id, array $searchparams = []): array {
        $sql = "SELECT COUNT(cicr.id) 
                FROM {cicomp_requirements} cicr
                JOIN {icomp} ic ON ic.id = cicr.icomp_id
                WHERE true AND cicr.cicomp_id = :cicomp_id";
        $whereparams = [];
        $whereparams['cicomp_id'] = $cicomp_id;
        return [$sql, $whereparams];
    }

    public function col_icomp_name($row): string {
        return format_string($row->icomp_name);
    }

    public function col_actions($row): string {
        global $OUTPUT;

        $menu = new action_menu();

        $actions = [];

        $deleteurl = new moodle_url(self::$delete_page_url, array('id' => $this->cicomp_id ,'delete' => $row->id, 'sesskey' => sesskey()));
        // Quick view.
        $actions['delete'] = [
            'url' => $deleteurl,
            'icon' => new pix_icon('t/delete', get_string('delete')),
            'string' => new lang_string('delete'),
            'attributes' => [
                'class' => 'action-delete',
                'data-action' => 'cicomp-req-delete',
                'data-requirement-id' => $row->id,
            ]
        ];

        $hasitems = false;
        foreach ($actions as $key => $action) {
            $hasitems = true;
            $menu->add(new action_menu_link(
                $action['url'],
                $action['icon'],
                $action['string'],
                false,
                $action['attributes']
            ));
        }
        if (!$hasitems) {
            return '';
        }
        return $OUTPUT->render($menu);
    }

    /**
     * Guess the base url for the table.
     *
     * @return void
     * @throws moodle_exception
     */
    public function guess_base_url(): void {
        $this->baseurl = new moodle_url('/local/iauthorise/management/company/cicompetency_details.php', ['id' => $this->cicomp_id]);
    }

    /**
     * Return the context for the table.
     *
     * @return context the context for the table
     * @throws dml_exception
     */
    public function get_context(): context {
        return context_system::instance();
    }
}
