<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Participants table filterset.
 *
 * @package    core
 * @category   table
 * @copyright  2020 Andrew <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

declare(strict_types=1);

namespace local_iauthorise\management\company\tables;

use core_table\local\filter\filterset;

/**
 * Participants table filterset.
 *
 * @package    core
 * @copyright  2020 <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class requirements_table_filterset extends filterset {
    /**
     * Get the required filters.
     *
     * No required filter.
     *
     * @return array.
     */
    public function get_required_filters(): array {
        return [];
    }

    /**
     * Get the optional filters.
     * No optional filter.
     * @return array
     */
    public function get_optional_filters(): array {
        return [];
    }
}
