#!/bin/bash

# Define the base directory (where the script is run from)
BASE_DIR=$(pwd)

# Define the destination directory for language files
DEST_DIR="/opt/homebrew/var/language_files_collection2"

# Create the destination directory if it doesn't exist
if [ ! -d "$DEST_DIR" ]; then
    mkdir -p "$DEST_DIR"
    echo "Created directory: $DEST_DIR"
fi

# Read the .gitmodules file and process each submodule
while IFS= read -r line; do
    if [[ $line =~ path[[:space:]]*=[[:space:]]*(.+)$ ]]; then
        # Extract the submodule path
        SUBMODULE_PATH="${BASH_REMATCH[1]}"

        # Extract the submodule name for creating a unique directory
        SUBMODULE_NAME=$(basename "$SUBMODULE_PATH")

        # Check if lang/en directory exists in the submodule
        if [ -d "$BASE_DIR/$SUBMODULE_PATH/lang/en" ]; then
            echo "Processing language files from: $SUBMODULE_PATH"

            # Create a directory for this submodule's language files
            mkdir -p "$DEST_DIR/$SUBMODULE_NAME"

            # Copy all PHP files from the lang/en directory
            cp "$BASE_DIR/$SUBMODULE_PATH/lang/en/"*.php "$DEST_DIR/$SUBMODULE_NAME/" 2>/dev/null

            # If copy was successful, print confirmation
            if [ $? -eq 0 ]; then
                echo "✓ Language files copied from $SUBMODULE_NAME"
            else
                echo "! No PHP files found in $SUBMODULE_PATH/lang/en"
            fi
        else
            echo "× No lang/en directory found in $SUBMODULE_PATH"
        fi
    fi
done < "$BASE_DIR/.gitmodules"

echo -e "\nLanguage files collection completed!"
echo "Files are stored in: $DEST_DIR"

# List all collected files
echo -e "\nCollected language files:"
find "$DEST_DIR" -type f -name "*.php" | while read -r file; do
    echo "- ${file#$BASE_DIR/}"
done
