This file describes API changes in /reportbuilder/*
Information provided here is intended especially for developers.

=== 4.1.3 ===

* New database helper method `sql_replace_parameter_names` to help ensure uniqueness of parameters within an expression (where
  that expression can be used multiple times as part of a larger query)
* The local report filter class has a new `get_field_sql_and_params` method which should be used by filter types that re-use
  the filter field SQL within their generated expression, to ensure SQL containing parameters works correctly

=== 4.1.2 ===

* The schedule helper `create_schedule` method accepts a `$timenow` parameter to use for comparisons against current date
  during tests

=== 4.1.1 ===

* The `datasource_stress_test_columns` test helper now enables sorting on those columns that support it
* The `create_[column|filter|condition]` test generator methods now allow for setting all persistent properties

=== 4.1 ===

* New method `add_action_divider()` in base system report class, to allow adding a divider to the action menu.
* New external method `core_reportbuilder_set_filters` for setting report filter values (plus `setFilters` AJAX repository
  export for calling from Javascript modules)
* New method `set_filter_form_default` in base system report class, to override whether the default filters form
  should be shown for a report
* The external `core_reportbuilder_filters_reset` method now accepts an optional `parameters` argument, required by
  some system reports
* New external methods for retrieving custom report data:
  - `core_reportbuilder_list_reports`
  - `core_reportbuilder_retrieve_report`
  - `core_reportbuilder_view_report`
* For consistency, the following entity classes have moved namespace (usage of previous namespace will generate debugging):
  - `core_admin\{ => reportbuilder}\local\entities\task_log`
  - `core_cohort\{ => reportbuilder}\local\entities\cohort`
  - `core_cohort\{ => reportbuilder}\local\entities\cohort_member`
  - `core_course\{ => reportbuilder}\local\entities\course_category`
  - `report_configlog\{ => reportbuilder}\local\entities\config_change`
* 'set_default_per_page' and 'get_default_per_page' methods have been added to \local\report\base class
  to manage the default displayed rows per page.
* Added two new methods in the datasource class:
  - add_all_from_entity() to add all columns/filters/conditions from the given entity to the report at once
  - add_all_from_entities() to add all columns/filters/conditions from all the entities added to the report at once
* New database helper methods for generating multiple unique values: `generate_aliases` and `generate_param_names`
* The base aggregation `format_value` method has a `$columntype` argument in order to preserve type during aggregation. When
  defining column callbacks, strict typing will now be preserved in your callback methods when the column is being aggregated
* The method `get_joins()` in the base entity class is now public, allowing for easier joins within reports
* New method `set_table_aliases` in base entity class, for overriding multiple table aliases in a single call
* The following local helper methods have been deprecated, their implementation moved to exporters:
  - `audience::get_all_audiences_menu_types` -> `custom_report_audience_cards_exporter`
  - `report::get_available_columns` -> `custom_report_column_cards_exporter`
* The `custom_report_exporter` class now defines its editor element properties as optional, rather than each of those exporters
  defining their own properties as optional. In turn, this means the structure of the following external methods will always be
  present and consistent:
  - `core_reportbuilder_columns_*`
  - `core_reportbuilder_conditions_*`
  - `core_reportbuilder_filters_*`
* The `custom_report_*` exporters now accept only classes that extend datasource as part of their related data properties
* The following permission methods now accept an optional `$context` parameter (default system context):
  - `[require_]can_view_reports_list`
  - `[require_]can_create_report`
* New method `get_default_condition_values()` in base datasource class, to be overridden by sources that wish to
  define default values for conditions upon report creation.
* New methods `get_identity_[column|filter]` in user entity, for retrieving user identity field report elements
* New method `user_reports_list_access_sql` in audience helper for retrieving list of all reports for given user
* New report filter types:
  - `category` for reports containing course categories
  - `tags` for reports containing entities with support for core_tag API
  - `autocomplete` for reports that contain pre-defined values for selection.
* New method `get_sample_values()` added to base filter class, to be overridden in all filter types to support stress testing
* New test helpers for automated stress testing of report sources:
  - `datasource_stress_test_columns`
  - `datasource_stress_test_columns_aggregation`
  - `datasource_stress_test_conditions`
* The test helper method `get_custom_report_content()` now accepts a list of filter values and applies them to the report
* New method `get_default_column_sorting` in base datasource class, to be overridden by sources that wish to
  define default columns sort order upon report creation.
