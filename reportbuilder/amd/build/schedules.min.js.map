{"version": 3, "file": "schedules.min.js", "sources": ["../src/schedules.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder audiences\n *\n * @module      core_reportbuilder/schedules\n * @copyright   2021 Paul <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport 'core/inplace_editable';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport {get_string as getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {createScheduleModal} from 'core_reportbuilder/local/repository/modals';\nimport {deleteSchedule, sendSchedule, toggleSchedule} from 'core_reportbuilder/local/repository/schedules';\n\nlet initialized = false;\n\n/**\n * Initialise schedules tab\n *\n * @param {Number} reportId\n */\nexport const init = reportId => {\n    prefetchStrings('core_reportbuilder', [\n        'deleteschedule',\n        'deletescheduleconfirm',\n        'disableschedule',\n        'editscheduledetails',\n        'enableschedule',\n        'newschedule',\n        'schedulecreated',\n        'scheduledeleted',\n        'schedulesent',\n        'scheduleupdated',\n        'sendschedule',\n        'sendscheduleconfirm',\n    ]);\n\n    prefetchStrings('core', [\n        'confirm',\n        'delete',\n    ]);\n\n    if (initialized) {\n        // We already added the event listeners (can be called multiple times by mustache template).\n        return;\n    }\n\n    document.addEventListener('click', event => {\n\n        // Create schedule.\n        const scheduleCreate = event.target.closest(reportSelectors.actions.scheduleCreate);\n        if (scheduleCreate) {\n            event.preventDefault();\n\n            const scheduleModal = createScheduleModal(event.target, getString('newschedule', 'core_reportbuilder'), reportId);\n            scheduleModal.addEventListener(scheduleModal.events.FORM_SUBMITTED, () => {\n                getString('schedulecreated', 'core_reportbuilder')\n                    .then(addToast)\n                    .then(() => {\n                        const reportElement = document.querySelector(reportSelectors.regions.report);\n                        dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                        return;\n                    })\n                    .catch(Notification.exception);\n            });\n\n            scheduleModal.show();\n        }\n\n        // Toggle schedule.\n        const scheduleToggle = event.target.closest(reportSelectors.actions.scheduleToggle);\n        if (scheduleToggle) {\n            const pendingPromise = new Pending('core_reportbuilder/schedules:toggle');\n            const scheduleStateToggle = +!Number(scheduleToggle.dataset.state);\n\n            toggleSchedule(reportId, scheduleToggle.dataset.id, scheduleStateToggle)\n                .then(() => {\n                    const tableRow = scheduleToggle.closest('tr');\n                    tableRow.classList.toggle('text-muted');\n\n                    scheduleToggle.dataset.state = scheduleStateToggle;\n\n                    const stringKey = scheduleStateToggle ? 'disableschedule' : 'enableschedule';\n                    return getString(stringKey, 'core_reportbuilder');\n                })\n                .then(toggleLabel => {\n                    const labelContainer = scheduleToggle.parentElement.querySelector(`label[for=\"${scheduleToggle.id}\"] > span`);\n                    labelContainer.innerHTML = toggleLabel;\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n\n        // Edit schedule.\n        const scheduleEdit = event.target.closest(reportSelectors.actions.scheduleEdit);\n        if (scheduleEdit) {\n            event.preventDefault();\n\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = scheduleEdit.closest('.dropdown').querySelector('.dropdown-toggle');\n            const scheduleModal = createScheduleModal(triggerElement, getString('editscheduledetails', 'core_reportbuilder'),\n                reportId, scheduleEdit.dataset.scheduleId);\n            scheduleModal.addEventListener(scheduleModal.events.FORM_SUBMITTED, () => {\n                getString('scheduleupdated', 'core_reportbuilder')\n                    .then(addToast)\n                    .then(() => {\n                        const reportElement = scheduleEdit.closest(reportSelectors.regions.report);\n                        dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                        return;\n                    })\n                    .catch(Notification.exception);\n            });\n\n            scheduleModal.show();\n        }\n\n        // Send schedule.\n        const scheduleSend = event.target.closest(reportSelectors.actions.scheduleSend);\n        if (scheduleSend) {\n            event.preventDefault();\n\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = scheduleSend.closest('.dropdown').querySelector('.dropdown-toggle');\n            Notification.saveCancelPromise(\n                getString('sendschedule', 'core_reportbuilder'),\n                getString('sendscheduleconfirm', 'core_reportbuilder', scheduleSend.dataset.scheduleName),\n                getString('confirm', 'core'),\n                {triggerElement}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/schedules:send');\n\n                return sendSchedule(reportId, scheduleSend.dataset.scheduleId)\n                    .then(addToast(getString('schedulesent', 'core_reportbuilder')))\n                    .then(() => pendingPromise.resolve())\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n\n        // Delete schedule.\n        const scheduleDelete = event.target.closest(reportSelectors.actions.scheduleDelete);\n        if (scheduleDelete) {\n            event.preventDefault();\n\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = scheduleDelete.closest('.dropdown').querySelector('.dropdown-toggle');\n            Notification.saveCancelPromise(\n                getString('deleteschedule', 'core_reportbuilder'),\n                getString('deletescheduleconfirm', 'core_reportbuilder', scheduleDelete.dataset.scheduleName),\n                getString('delete', 'core'),\n                {triggerElement}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/schedules:delete');\n\n                return deleteSchedule(reportId, scheduleDelete.dataset.scheduleId)\n                    .then(addToast(getString('scheduledeleted', 'core_reportbuilder')))\n                    .then(() => {\n                        const reportElement = scheduleDelete.closest(reportSelectors.regions.report);\n                        dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                        return pendingPromise.resolve();\n                    })\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n\n    initialized = true;\n};\n"], "names": ["initialized", "reportId", "document", "addEventListener", "event", "target", "closest", "reportSelectors", "actions", "scheduleCreate", "preventDefault", "scheduleModal", "events", "FORM_SUBMITTED", "then", "addToast", "reportElement", "querySelector", "regions", "report", "reportEvents", "tableReload", "catch", "Notification", "exception", "show", "scheduleToggle", "pendingPromise", "Pending", "scheduleStateToggle", "Number", "dataset", "state", "id", "classList", "toggle", "<PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON>", "parentElement", "innerHTML", "resolve", "scheduleEdit", "triggerElement", "scheduleId", "scheduleSend", "saveCancelPromise", "scheduleName", "scheduleDelete", "preservePagination"], "mappings": "0wDAqCIA,aAAc,gBAOEC,yCACA,qBAAsB,CAClC,iBACA,wBACA,kBACA,sBACA,iBACA,cACA,kBACA,kBACA,eACA,kBACA,eACA,sDAGY,OAAQ,CACpB,UACA,WAGAD,cAKJE,SAASC,iBAAiB,SAASC,WAGRA,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQC,gBAChD,CAChBL,MAAMM,uBAEAC,eAAgB,+BAAoBP,MAAMC,QAAQ,mBAAU,cAAe,sBAAuBJ,UACxGU,cAAcR,iBAAiBQ,cAAcC,OAAOC,gBAAgB,yBACtD,kBAAmB,sBACxBC,KAAKC,YACLD,MAAK,WACIE,cAAgBd,SAASe,cAAcV,gBAAgBW,QAAQC,4CACvDC,aAAaC,YAAa,GAAIL,kBAG/CM,MAAMC,sBAAaC,cAG5Bb,cAAcc,aAIZC,eAAiBtB,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQkB,mBAChEA,eAAgB,OACVC,eAAiB,IAAIC,iBAAQ,uCAC7BC,sBAAwBC,OAAOJ,eAAeK,QAAQC,qCAE7C/B,SAAUyB,eAAeK,QAAQE,GAAIJ,qBAC/Cf,MAAK,KACeY,eAAepB,QAAQ,MAC/B4B,UAAUC,OAAO,cAE1BT,eAAeK,QAAQC,MAAQH,0BAEzBO,UAAYP,oBAAsB,kBAAoB,wBACrD,mBAAUO,UAAW,yBAE/BtB,MAAKuB,cACqBX,eAAeY,cAAcrB,mCAA4BS,eAAeO,iBAChFM,UAAYF,YACpBV,eAAea,aAEzBlB,MAAMC,sBAAaC,iBAItBiB,aAAerC,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQiC,iBAC9DA,aAAc,CACdrC,MAAMM,uBAGAgC,eAAiBD,aAAanC,QAAQ,aAAaW,cAAc,oBACjEN,eAAgB,+BAAoB+B,gBAAgB,mBAAU,sBAAuB,sBACvFzC,SAAUwC,aAAaV,QAAQY,YACnChC,cAAcR,iBAAiBQ,cAAcC,OAAOC,gBAAgB,yBACtD,kBAAmB,sBACxBC,KAAKC,YACLD,MAAK,WACIE,cAAgByB,aAAanC,QAAQC,gBAAgBW,QAAQC,4CACrDC,aAAaC,YAAa,GAAIL,kBAG/CM,MAAMC,sBAAaC,cAG5Bb,cAAcc,aAIZmB,aAAexC,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQoC,iBAC9DA,aAAc,CACdxC,MAAMM,uBAGAgC,eAAiBE,aAAatC,QAAQ,aAAaW,cAAc,0CAC1D4B,mBACT,mBAAU,eAAgB,uBAC1B,mBAAU,sBAAuB,qBAAsBD,aAAab,QAAQe,eAC5E,mBAAU,UAAW,QACrB,CAACJ,eAAAA,iBACH5B,MAAK,WACGa,eAAiB,IAAIC,iBAAQ,4CAE5B,2BAAa3B,SAAU2C,aAAab,QAAQY,YAC9C7B,MAAK,eAAS,mBAAU,eAAgB,wBACxCA,MAAK,IAAMa,eAAea,YAC1BlB,MAAMC,sBAAaC,cACzBF,OAAM,eAMPyB,eAAiB3C,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQuC,mBAChEA,eAAgB,CAChB3C,MAAMM,uBAGAgC,eAAiBK,eAAezC,QAAQ,aAAaW,cAAc,0CAC5D4B,mBACT,mBAAU,iBAAkB,uBAC5B,mBAAU,wBAAyB,qBAAsBE,eAAehB,QAAQe,eAChF,mBAAU,SAAU,QACpB,CAACJ,eAAAA,iBACH5B,MAAK,WACGa,eAAiB,IAAIC,iBAAQ,8CAE5B,6BAAe3B,SAAU8C,eAAehB,QAAQY,YAClD7B,MAAK,eAAS,mBAAU,kBAAmB,wBAC3CA,MAAK,WACIE,cAAgB+B,eAAezC,QAAQC,gBAAgBW,QAAQC,kDACvDC,aAAaC,YAAa,CAAC2B,oBAAoB,GAAOhC,eAC7DW,eAAea,aAEzBlB,MAAMC,sBAAaC,cACzBF,OAAM,aAMjBtB,aAAc"}