{"version": 3, "file": "reports_list.min.js", "sources": ["../src/reports_list.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder reports list management\n *\n * @module      core_reportbuilder/reports_list\n * @copyright   2021 David Matamoros <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport {get_string as getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {deleteReport} from 'core_reportbuilder/local/repository/reports';\nimport {createReportModal} from 'core_reportbuilder/local/repository/modals';\n\n/**\n * Initialise module\n */\nexport const init = () => {\n    prefetchStrings('core_reportbuilder', [\n        'deletereport',\n        'deletereportconfirm',\n        'editreportdetails',\n        'newreport',\n        'reportdeleted',\n        'reportupdated',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    document.addEventListener('click', event => {\n        const reportCreate = event.target.closest(reportSelectors.actions.reportCreate);\n        if (reportCreate) {\n            event.preventDefault();\n\n            // Redirect user to editing interface for the report after submission.\n            const reportModal = createReportModal(event.target, getString('newreport', 'core_reportbuilder'));\n            reportModal.addEventListener(reportModal.events.FORM_SUBMITTED, event => {\n                window.location.href = event.detail;\n            });\n\n            reportModal.show();\n        }\n\n        const reportEdit = event.target.closest(reportSelectors.actions.reportEdit);\n        if (reportEdit) {\n            event.preventDefault();\n\n            // Reload current report page after submission.\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = reportEdit.closest('.dropdown').querySelector('.dropdown-toggle');\n            const reportModal = createReportModal(triggerElement, getString('editreportdetails', 'core_reportbuilder'),\n                reportEdit.dataset.reportId);\n            reportModal.addEventListener(reportModal.events.FORM_SUBMITTED, () => {\n                const reportElement = event.target.closest(reportSelectors.regions.report);\n\n                getString('reportupdated', 'core_reportbuilder')\n                    .then(addToast)\n                    .then(() => {\n                        dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                        return;\n                    })\n                    .catch(Notification.exception);\n            });\n\n            reportModal.show();\n        }\n\n        const reportDelete = event.target.closest(reportSelectors.actions.reportDelete);\n        if (reportDelete) {\n            event.preventDefault();\n\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = reportDelete.closest('.dropdown').querySelector('.dropdown-toggle');\n            Notification.saveCancelPromise(\n                getString('deletereport', 'core_reportbuilder'),\n                getString('deletereportconfirm', 'core_reportbuilder', reportDelete.dataset.reportName),\n                getString('delete', 'core'),\n                {triggerElement}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/reports:delete');\n                const reportElement = event.target.closest(reportSelectors.regions.report);\n\n                return deleteReport(reportDelete.dataset.reportId)\n                    .then(() => addToast(getString('reportdeleted', 'core_reportbuilder')))\n                    .then(() => {\n                        dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                        return pendingPromise.resolve();\n                    })\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n};\n"], "names": ["document", "addEventListener", "event", "target", "closest", "reportSelectors", "actions", "reportCreate", "preventDefault", "reportModal", "events", "FORM_SUBMITTED", "window", "location", "href", "detail", "show", "reportEdit", "triggerElement", "querySelector", "dataset", "reportId", "reportElement", "regions", "report", "then", "addToast", "reportEvents", "tableReload", "preservePagination", "catch", "Notification", "exception", "reportDelete", "saveCancelPromise", "reportName", "pendingPromise", "Pending", "resolve"], "mappings": "yuDAuCoB,mCACA,qBAAsB,CAClC,eACA,sBACA,oBACA,YACA,gBACA,gDAGY,OAAQ,CACpB,WAGJA,SAASC,iBAAiB,SAASC,WACVA,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQC,cAChD,CACdL,MAAMM,uBAGAC,aAAc,6BAAkBP,MAAMC,QAAQ,mBAAU,YAAa,uBAC3EM,YAAYR,iBAAiBQ,YAAYC,OAAOC,gBAAgBT,QAC5DU,OAAOC,SAASC,KAAOZ,MAAMa,UAGjCN,YAAYO,aAGVC,WAAaf,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQW,eAC5DA,WAAY,CACZf,MAAMM,uBAIAU,eAAiBD,WAAWb,QAAQ,aAAae,cAAc,oBAC/DV,aAAc,6BAAkBS,gBAAgB,mBAAU,oBAAqB,sBACjFD,WAAWG,QAAQC,UACvBZ,YAAYR,iBAAiBQ,YAAYC,OAAOC,gBAAgB,WACtDW,cAAgBpB,MAAMC,OAAOC,QAAQC,gBAAgBkB,QAAQC,4BAEzD,gBAAiB,sBACtBC,KAAKC,YACLD,MAAK,yCACYE,aAAaC,YAAa,CAACC,oBAAoB,GAAOP,kBAGvEQ,MAAMC,sBAAaC,cAG5BvB,YAAYO,aAGViB,aAAe/B,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQ2B,iBAC9DA,aAAc,CACd/B,MAAMM,uBAGAU,eAAiBe,aAAa7B,QAAQ,aAAae,cAAc,0CAC1De,mBACT,mBAAU,eAAgB,uBAC1B,mBAAU,sBAAuB,qBAAsBD,aAAab,QAAQe,aAC5E,mBAAU,SAAU,QACpB,CAACjB,eAAAA,iBACHO,MAAK,WACGW,eAAiB,IAAIC,iBAAQ,qCAC7Bf,cAAgBpB,MAAMC,OAAOC,QAAQC,gBAAgBkB,QAAQC,eAE5D,yBAAaS,aAAab,QAAQC,UACpCI,MAAK,KAAM,eAAS,mBAAU,gBAAiB,yBAC/CA,MAAK,yCACYE,aAAaC,YAAa,CAACC,oBAAoB,GAAOP,eAC7Dc,eAAeE,aAEzBR,MAAMC,sBAAaC,cACzBF,OAAM"}