{"version": 3, "file": "report.min.js", "sources": ["../src/report.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder report management\n *\n * @module      core_reportbuilder/report\n * @copyright   2021 Paul <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Notification from 'core/notification';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {setPageNumber, refreshTableContent} from 'core_table/dynamic';\nimport * as tableSelectors from 'core_table/local/dynamic/selectors';\n\nconst CLASSES = {\n    COLLAPSED: 'collapsed',\n    EXPANDED: 'show',\n    ICONUP: 'fa-angle-up',\n    ICONDOWN: 'fa-angle-down'\n};\n\nlet initialized = false;\n\n/**\n * Initialise module for given report\n *\n * @method\n */\nexport const init = () => {\n\n    if (initialized) {\n        // We already added the event listeners (can be called multiple times by mustache template).\n        return;\n    }\n\n    // Listen for the table reload event.\n    document.addEventListener(reportEvents.tableReload, async(event) => {\n        const reportElement = event.target.closest(reportSelectors.regions.report);\n        if (reportElement === null) {\n            return;\n        }\n\n        const tableRoot = reportElement.querySelector(tableSelectors.main.region);\n        const pageNumber = event.detail?.preservePagination ? null : 1;\n\n        await setPageNumber(tableRoot, pageNumber, false)\n            .then(refreshTableContent)\n            .then(() => {\n                // TODO: Refactor this after MDL-73130 lands.\n                const preserveTriggerElement = event.detail?.preserveTriggerElement;\n                if (preserveTriggerElement) {\n                    reportElement.querySelector(preserveTriggerElement)?.focus();\n                }\n                return;\n            })\n            .catch(Notification.exception);\n    });\n\n    // Listen for trigger popup events.\n    document.addEventListener('click', event => {\n        const reportActionPopup = event.target.closest(reportSelectors.actions.reportActionPopup);\n        if (reportActionPopup === null) {\n            return;\n        }\n        event.preventDefault();\n        const popupAction = JSON.parse(reportActionPopup.dataset.popupAction);\n        window.openpopup(event, popupAction.jsfunctionargs);\n    });\n\n    // Listen for card view toggle events.\n    document.addEventListener('click', (event) => {\n        const toggleCard = event.target.closest(reportSelectors.actions.toggleCardView);\n        if (toggleCard) {\n            const tableCard = toggleCard.closest('tr');\n            const toggleIcon = toggleCard.querySelector('i');\n            event.preventDefault();\n            if (toggleCard.classList.contains(CLASSES.COLLAPSED)) {\n                tableCard.classList.add(CLASSES.EXPANDED);\n                toggleIcon.classList.replace(CLASSES.ICONDOWN, CLASSES.ICONUP);\n                toggleCard.classList.remove(CLASSES.COLLAPSED);\n                toggleCard.setAttribute('aria-expanded', \"true\");\n            } else {\n                tableCard.classList.remove(CLASSES.EXPANDED);\n                toggleIcon.classList.replace(CLASSES.ICONUP, CLASSES.ICONDOWN);\n                toggleCard.classList.add(CLASSES.COLLAPSED);\n                toggleCard.removeAttribute('aria-expanded');\n            }\n        }\n    });\n\n    initialized = true;\n};\n"], "names": ["CLASSES", "initialized", "document", "addEventListener", "reportEvents", "tableReload", "async", "reportElement", "event", "target", "closest", "reportSelectors", "regions", "report", "tableRoot", "querySelector", "tableSelectors", "main", "region", "pageNumber", "detail", "preservePagination", "then", "refreshTableContent", "preserveTriggerElement", "_event$detail2", "focus", "catch", "Notification", "exception", "reportActionPopup", "actions", "preventDefault", "popupAction", "JSON", "parse", "dataset", "window", "openpopup", "jsfunctionargs", "toggleCard", "toggleCardView", "tableCard", "toggleIcon", "classList", "contains", "add", "replace", "remove", "setAttribute", "removeAttribute"], "mappings": ";;;;;;;svCA6BMA,kBACS,YADTA,iBAEQ,OAFRA,eAGM,cAHNA,iBAIQ,oBAGVC,aAAc,gBAOE,KAEZA,cAMJC,SAASC,iBAAiBC,aAAaC,aAAaC,MAAAA,gCAC1CC,cAAgBC,MAAMC,OAAOC,QAAQC,gBAAgBC,QAAQC,WAC7C,OAAlBN,2BAIEO,UAAYP,cAAcQ,cAAcC,eAAeC,KAAKC,QAC5DC,iCAAaX,MAAMY,+CAAQC,mBAAqB,KAAO,QAEvD,0BAAcP,UAAWK,YAAY,GACtCG,KAAKC,8BACLD,MAAK,8BAEIE,8CAAyBhB,MAAMY,wCAANK,eAAcD,iDACzCA,uDACAjB,cAAcQ,cAAcS,gFAAyBE,YAI5DC,MAAMC,sBAAaC,cAI5B3B,SAASC,iBAAiB,SAASK,cACzBsB,kBAAoBtB,MAAMC,OAAOC,QAAQC,gBAAgBoB,QAAQD,sBAC7C,OAAtBA,yBAGJtB,MAAMwB,uBACAC,YAAcC,KAAKC,MAAML,kBAAkBM,QAAQH,aACzDI,OAAOC,UAAU9B,MAAOyB,YAAYM,mBAIxCrC,SAASC,iBAAiB,SAAUK,cAC1BgC,WAAahC,MAAMC,OAAOC,QAAQC,gBAAgBoB,QAAQU,mBAC5DD,WAAY,OACNE,UAAYF,WAAW9B,QAAQ,MAC/BiC,WAAaH,WAAWzB,cAAc,KAC5CP,MAAMwB,iBACFQ,WAAWI,UAAUC,SAAS7C,oBAC9B0C,UAAUE,UAAUE,IAAI9C,kBACxB2C,WAAWC,UAAUG,QAAQ/C,iBAAkBA,gBAC/CwC,WAAWI,UAAUI,OAAOhD,mBAC5BwC,WAAWS,aAAa,gBAAiB,UAEzCP,UAAUE,UAAUI,OAAOhD,kBAC3B2C,WAAWC,UAAUG,QAAQ/C,eAAgBA,kBAC7CwC,WAAWI,UAAUE,IAAI9C,mBACzBwC,WAAWU,gBAAgB,sBAKvCjD,aAAc"}