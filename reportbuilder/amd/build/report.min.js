define("core_reportbuilder/report",["exports","core/notification","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core_table/dynamic","core_table/local/dynamic/selectors"],(function(_exports,_notification,reportEvents,reportSelectors,_dynamic,tableSelectors){var obj;
/**
   * Report builder report management
   *
   * @module      core_reportbuilder/report
   * @copyright   2021 Paul <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=(obj=_notification)&&obj.__esModule?obj:{default:obj},reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors),tableSelectors=_interopRequireWildcard(tableSelectors);const CLASSES_COLLAPSED="collapsed",CLASSES_EXPANDED="show",CLASSES_ICONUP="fa-angle-up",CLASSES_ICONDOWN="fa-angle-down";let initialized=!1;_exports.init=()=>{initialized||(document.addEventListener(reportEvents.tableReload,(async event=>{var _event$detail;const reportElement=event.target.closest(reportSelectors.regions.report);if(null===reportElement)return;const tableRoot=reportElement.querySelector(tableSelectors.main.region),pageNumber=null!==(_event$detail=event.detail)&&void 0!==_event$detail&&_event$detail.preservePagination?null:1;await(0,_dynamic.setPageNumber)(tableRoot,pageNumber,!1).then(_dynamic.refreshTableContent).then((()=>{var _event$detail2;const preserveTriggerElement=null===(_event$detail2=event.detail)||void 0===_event$detail2?void 0:_event$detail2.preserveTriggerElement;var _reportElement$queryS;preserveTriggerElement&&(null===(_reportElement$queryS=reportElement.querySelector(preserveTriggerElement))||void 0===_reportElement$queryS||_reportElement$queryS.focus())})).catch(_notification.default.exception)})),document.addEventListener("click",(event=>{const reportActionPopup=event.target.closest(reportSelectors.actions.reportActionPopup);if(null===reportActionPopup)return;event.preventDefault();const popupAction=JSON.parse(reportActionPopup.dataset.popupAction);window.openpopup(event,popupAction.jsfunctionargs)})),document.addEventListener("click",(event=>{const toggleCard=event.target.closest(reportSelectors.actions.toggleCardView);if(toggleCard){const tableCard=toggleCard.closest("tr"),toggleIcon=toggleCard.querySelector("i");event.preventDefault(),toggleCard.classList.contains(CLASSES_COLLAPSED)?(tableCard.classList.add(CLASSES_EXPANDED),toggleIcon.classList.replace(CLASSES_ICONDOWN,CLASSES_ICONUP),toggleCard.classList.remove(CLASSES_COLLAPSED),toggleCard.setAttribute("aria-expanded","true")):(tableCard.classList.remove(CLASSES_EXPANDED),toggleIcon.classList.replace(CLASSES_ICONUP,CLASSES_ICONDOWN),toggleCard.classList.add(CLASSES_COLLAPSED),toggleCard.removeAttribute("aria-expanded"))}})),initialized=!0)}}));

//# sourceMappingURL=report.min.js.map