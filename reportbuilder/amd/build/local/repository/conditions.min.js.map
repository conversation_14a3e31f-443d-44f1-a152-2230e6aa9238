{"version": 3, "file": "conditions.min.js", "sources": ["../../../src/local/repository/conditions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle condition AJAX requests\n *\n * @module      core_reportbuilder/local/repository/conditions\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Reset all conditions for given report\n *\n * @param {Number} reportId\n * @return {Promise}\n */\nexport const resetConditions = reportId => {\n    const request = {\n        methodname: 'core_reportbuilder_conditions_reset',\n        args: {reportid: reportId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Add condition to given report\n *\n * @param {Number} reportId\n * @param {String} uniqueIdentifier\n * @return {Promise}\n */\nexport const addCondition = (reportId, uniqueIdentifier) => {\n    const request = {\n        methodname: 'core_reportbuilder_conditions_add',\n        args: {reportid: reportId, uniqueidentifier: uniqueIdentifier}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Remove condition from given report\n *\n * @param {Number} reportId\n * @param {Number} conditionId\n * @return {Promise}\n */\nexport const deleteCondition = (reportId, conditionId) => {\n    const request = {\n        methodname: 'core_reportbuilder_conditions_delete',\n        args: {reportid: reportId, conditionid: conditionId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Reorder a condition in a given report\n *\n * @param {Number} reportId\n * @param {Number} conditionId\n * @param {Number} position\n * @return {Promise}\n */\nexport const reorderCondition = (reportId, conditionId, position) => {\n    const request = {\n        methodname: 'core_reportbuilder_conditions_reorder',\n        args: {reportid: reportId, conditionid: conditionId, position: position}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "request", "methodname", "args", "reportid", "Ajax", "call", "uniqueIdentifier", "uniqueidentifier", "conditionId", "conditionid", "position"], "mappings": ";;;;;;;mPA+B+BA,iBACrBC,QAAU,CACZC,WAAY,sCACZC,KAAM,CAACC,SAAUJ,kBAGdK,cAAKC,KAAK,CAACL,UAAU,0BAUJ,CAACD,SAAUO,0BAC7BN,QAAU,CACZC,WAAY,oCACZC,KAAM,CAACC,SAAUJ,SAAUQ,iBAAkBD,0BAG1CF,cAAKC,KAAK,CAACL,UAAU,6BAUD,CAACD,SAAUS,qBAChCR,QAAU,CACZC,WAAY,uCACZC,KAAM,CAACC,SAAUJ,SAAUU,YAAaD,qBAGrCJ,cAAKC,KAAK,CAACL,UAAU,8BAWA,CAACD,SAAUS,YAAaE,kBAC9CV,QAAU,CACZC,WAAY,wCACZC,KAAM,CAACC,SAAUJ,SAAUU,YAAaD,YAAaE,SAAUA,kBAG5DN,cAAKC,KAAK,CAACL,UAAU"}