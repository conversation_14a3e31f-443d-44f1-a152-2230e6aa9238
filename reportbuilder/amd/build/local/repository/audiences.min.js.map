{"version": 3, "file": "audiences.min.js", "sources": ["../../../src/local/repository/audiences.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle audiences AJAX requests\n *\n * @module      core_reportbuilder/local/repository/audiences\n * @copyright   2021 David <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Remove audience from given report\n *\n * @param {Number} reportId\n * @param {Number} instanceId\n * @return {Promise}\n */\nexport const deleteAudience = (reportId, instanceId) => {\n    const request = {\n        methodname: 'core_reportbuilder_audiences_delete',\n        args: {reportid: reportId, instanceid: instanceId}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "instanceId", "request", "methodname", "args", "reportid", "instanceid", "Ajax", "call"], "mappings": ";;;;;;;wKAgC8B,CAACA,SAAUC,oBAC/BC,QAAU,CACZC,WAAY,sCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,oBAGpCM,cAAKC,KAAK,CAACN,UAAU"}