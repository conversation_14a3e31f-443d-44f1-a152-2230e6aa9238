{"version": 3, "file": "filters.min.js", "sources": ["../../../src/local/repository/filters.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle filter AJAX requests\n *\n * @module      core_reportbuilder/local/repository/filters\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Reset all filters for given report\n *\n * @method\n * @param {Number} reportId\n * @param {String} reportParameters\n * @return {Promise}\n */\nexport const resetFilters = (reportId, reportParameters) => {\n    const request = {\n        methodname: 'core_reportbuilder_filters_reset',\n        args: {reportid: reportId, parameters: reportParameters}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Set filter values for given report\n *\n * @method\n * @param {Number} reportId\n * @param {String} reportParameters\n * @param {String} filterValues\n * @return {Promise}\n */\nexport const setFilters = (reportId, reportParameters, filterValues) => {\n    const request = {\n        methodname: 'core_reportbuilder_set_filters',\n        args: {reportid: reportId, parameters: reportParameters, values: filterValues}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Add a filter to the given report\n *\n * @param {Number} reportId\n * @param {String} uniqueIdentifier\n * @return {Promise}\n */\nexport const addFilter = (reportId, uniqueIdentifier) => {\n    const request = {\n        methodname: 'core_reportbuilder_filters_add',\n        args: {reportid: reportId, uniqueidentifier: uniqueIdentifier}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Remove filter from given report\n *\n * @param {Number} reportId\n * @param {Number} filterId\n * @return {Promise}\n */\nexport const deleteFilter = (reportId, filterId) => {\n    const request = {\n        methodname: 'core_reportbuilder_filters_delete',\n        args: {reportid: reportId, filterid: filterId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Reorder a filter in a given report\n *\n * @param {Number} reportId\n * @param {Number} filterId\n * @param {Number} position\n * @return {Promise}\n */\nexport const reorderFilter = (reportId, filterId, position) => {\n    const request = {\n        methodname: 'core_reportbuilder_filters_reorder',\n        args: {reportid: reportId, filterid: filterId, position: position}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "reportParameters", "request", "methodname", "args", "reportid", "parameters", "Ajax", "call", "filterValues", "values", "uniqueIdentifier", "uniqueidentifier", "filterId", "filterid", "position"], "mappings": ";;;;;;;wPAiC4B,CAACA,SAAUC,0BAC7BC,QAAU,CACZC,WAAY,mCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,0BAGpCM,cAAKC,KAAK,CAACN,UAAU,wBAYN,CAACF,SAAUC,iBAAkBQ,sBAC7CP,QAAU,CACZC,WAAY,iCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,iBAAkBS,OAAQD,sBAG9DF,cAAKC,KAAK,CAACN,UAAU,uBAUP,CAACF,SAAUW,0BAC1BT,QAAU,CACZC,WAAY,iCACZC,KAAM,CAACC,SAAUL,SAAUY,iBAAkBD,0BAG1CJ,cAAKC,KAAK,CAACN,UAAU,0BAUJ,CAACF,SAAUa,kBAC7BX,QAAU,CACZC,WAAY,oCACZC,KAAM,CAACC,SAAUL,SAAUc,SAAUD,kBAGlCN,cAAKC,KAAK,CAACN,UAAU,2BAWH,CAACF,SAAUa,SAAUE,kBACxCb,QAAU,CACZC,WAAY,qCACZC,KAAM,CAACC,SAAUL,SAAUc,SAAUD,SAAUE,SAAUA,kBAGtDR,cAAKC,KAAK,CAACN,UAAU"}