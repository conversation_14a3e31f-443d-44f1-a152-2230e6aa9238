define("core_reportbuilder/local/repository/audiences",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle audiences AJAX requests
   *
   * @module      core_reportbuilder/local/repository/audiences
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.deleteAudience=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.deleteAudience=(reportId,instanceId)=>{const request={methodname:"core_reportbuilder_audiences_delete",args:{reportid:reportId,instanceid:instanceId}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=audiences.min.js.map