{"version": 3, "file": "sorting.min.js", "sources": ["../../../src/local/repository/sorting.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle column sorting AJAX requests\n *\n * @module      core_reportbuilder/local/repository/sorting\n * @copyright   2021 David <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport <PERSON> from 'core/ajax';\n\n/**\n * Retrieve column sorting\n *\n * @param {Number} reportId\n * @return {Promise}\n */\nexport const getColumnSorting = reportId => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_sort_get',\n        args: {reportid: reportId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Re-order sort column position\n *\n * @param {Number} reportId\n * @param {Number} columnId\n * @param {Number} position\n * @return {Promise}\n */\nexport const reorderColumnSorting = (reportId, columnId, position) => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_sort_reorder',\n        args: {reportid: reportId, columnid: columnId, position: position}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Enables/disabled sorting on column\n *\n * @param {Number} reportId\n * @param {Number} columnId\n * @param {Boolean} enabled\n * @param {Number} direction\n * @return {Promise}\n */\nexport const toggleColumnSorting = (reportId, columnId, enabled, direction) => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_sort_toggle',\n        args: {reportid: reportId, columnid: columnId, enabled: enabled, direction: direction}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "request", "methodname", "args", "reportid", "Ajax", "call", "columnId", "position", "columnid", "enabled", "direction"], "mappings": ";;;;;;;uOA+BgCA,iBACtBC,QAAU,CACZC,WAAY,sCACZC,KAAM,CAACC,SAAUJ,kBAGdK,cAAKC,KAAK,CAACL,UAAU,kCAWI,CAACD,SAAUO,SAAUC,kBAC/CP,QAAU,CACZC,WAAY,0CACZC,KAAM,CAACC,SAAUJ,SAAUS,SAAUF,SAAUC,SAAUA,kBAGtDH,cAAKC,KAAK,CAACL,UAAU,iCAYG,CAACD,SAAUO,SAAUG,QAASC,mBACvDV,QAAU,CACZC,WAAY,yCACZC,KAAM,CAACC,SAAUJ,SAAUS,SAAUF,SAAUG,QAASA,QAASC,UAAWA,mBAGzEN,cAAKC,KAAK,CAACL,UAAU"}