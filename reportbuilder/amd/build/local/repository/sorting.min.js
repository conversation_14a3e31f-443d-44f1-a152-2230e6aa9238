define("core_reportbuilder/local/repository/sorting",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle column sorting AJAX requests
   *
   * @module      core_reportbuilder/local/repository/sorting
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.toggleColumnSorting=_exports.reorderColumnSorting=_exports.getColumnSorting=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.getColumnSorting=reportId=>{const request={methodname:"core_reportbuilder_columns_sort_get",args:{reportid:reportId}};return _ajax.default.call([request])[0]};_exports.reorderColumnSorting=(reportId,columnId,position)=>{const request={methodname:"core_reportbuilder_columns_sort_reorder",args:{reportid:reportId,columnid:columnId,position:position}};return _ajax.default.call([request])[0]};_exports.toggleColumnSorting=(reportId,columnId,enabled,direction)=>{const request={methodname:"core_reportbuilder_columns_sort_toggle",args:{reportid:reportId,columnid:columnId,enabled:enabled,direction:direction}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=sorting.min.js.map