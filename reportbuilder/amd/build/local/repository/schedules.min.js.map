{"version": 3, "file": "schedules.min.js", "sources": ["../../../src/local/repository/schedules.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle schedule AJAX requests\n *\n * @module      core_reportbuilder/local/repository/schedules\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport <PERSON> from 'core/ajax';\n\n/**\n * Delete schedule\n *\n * @method\n * @param {Number} reportId\n * @param {Number} scheduleId\n * @return {Promise}\n */\nexport const deleteSchedule = (reportId, scheduleId) => {\n    const request = {\n        methodname: 'core_reportbuilder_schedules_delete',\n        args: {reportid: reportId, scheduleid: scheduleId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Send schedule\n *\n * @method\n * @param {Number} reportId\n * @param {Number} scheduleId\n * @return {Promise}\n */\nexport const sendSchedule = (reportId, scheduleId) => {\n    const request = {\n        methodname: 'core_reportbuilder_schedules_send',\n        args: {reportid: reportId, scheduleid: scheduleId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Toggle schedule enabled\n *\n * @method\n * @param {Number} reportId\n * @param {Number} scheduleId\n * @param {Boolean} scheduleEnabled\n * @return {Promise}\n */\nexport const toggleSchedule = (reportId, scheduleId, scheduleEnabled) => {\n    const request = {\n        methodname: 'core_reportbuilder_schedules_toggle',\n        args: {reportid: reportId, scheduleid: scheduleId, enabled: scheduleEnabled}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "scheduleId", "request", "methodname", "args", "reportid", "scheduleid", "Ajax", "call", "scheduleEnabled", "enabled"], "mappings": ";;;;;;;sNAiC8B,CAACA,SAAUC,oBAC/BC,QAAU,CACZC,WAAY,sCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,oBAGpCM,cAAKC,KAAK,CAACN,UAAU,0BAWJ,CAACF,SAAUC,oBAC7BC,QAAU,CACZC,WAAY,oCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,oBAGpCM,cAAKC,KAAK,CAACN,UAAU,4BAYF,CAACF,SAAUC,WAAYQ,yBAC3CP,QAAU,CACZC,WAAY,sCACZC,KAAM,CAACC,SAAUL,SAAUM,WAAYL,WAAYS,QAASD,yBAGzDF,cAAKC,KAAK,CAACN,UAAU"}