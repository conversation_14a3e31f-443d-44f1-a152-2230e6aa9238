define("core_reportbuilder/local/repository/columns",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle column AJAX requests
   *
   * @module      core_reportbuilder/local/repository/columns
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.reorderColumn=_exports.deleteColumn=_exports.addColumn=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.addColumn=(reportId,uniqueIdentifier)=>{const request={methodname:"core_reportbuilder_columns_add",args:{reportid:reportId,uniqueidentifier:uniqueIdentifier}};return _ajax.default.call([request])[0]};_exports.deleteColumn=(reportId,columnId)=>{const request={methodname:"core_reportbuilder_columns_delete",args:{reportid:reportId,columnid:columnId}};return _ajax.default.call([request])[0]};_exports.reorderColumn=(reportId,columnId,position)=>{const request={methodname:"core_reportbuilder_columns_reorder",args:{reportid:reportId,columnid:columnId,position:position}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=columns.min.js.map