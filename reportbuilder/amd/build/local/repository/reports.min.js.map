{"version": 3, "file": "reports.min.js", "sources": ["../../../src/local/repository/reports.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle report AJAX requests\n *\n * @module      core_reportbuilder/local/repository/reports\n * @copyright   2021 Paul <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Delete given report\n *\n * @param {Number} reportId\n * @return {Promise}\n */\nexport const deleteReport = reportId => {\n    const request = {\n        methodname: 'core_reportbuilder_reports_delete',\n        args: {reportid: reportId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Get report content\n *\n * @param {Number} reportId\n * @param {Boolean} editMode\n * @return {Promise}\n */\nexport const getReport = (reportId, editMode) => {\n    const request = {\n        methodname: 'core_reportbuilder_reports_get',\n        args: {reportid: reportId, editmode: editMode}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "request", "methodname", "args", "reportid", "Ajax", "call", "editMode", "editmode"], "mappings": ";;;;;;;uLA+B4BA,iBAClBC,QAAU,CACZC,WAAY,oCACZC,KAAM,CAACC,SAAUJ,kBAGdK,cAAKC,KAAK,CAACL,UAAU,uBAUP,CAACD,SAAUO,kBAC1BN,QAAU,CACZC,WAAY,iCACZC,KAAM,CAACC,SAAUJ,SAAUQ,SAAUD,kBAGlCF,cAAKC,KAAK,CAACL,UAAU"}