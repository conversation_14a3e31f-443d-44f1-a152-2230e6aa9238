define("core_reportbuilder/local/repository/conditions",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle condition AJAX requests
   *
   * @module      core_reportbuilder/local/repository/conditions
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.resetConditions=_exports.reorderCondition=_exports.deleteCondition=_exports.addCondition=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.resetConditions=reportId=>{const request={methodname:"core_reportbuilder_conditions_reset",args:{reportid:reportId}};return _ajax.default.call([request])[0]};_exports.addCondition=(reportId,uniqueIdentifier)=>{const request={methodname:"core_reportbuilder_conditions_add",args:{reportid:reportId,uniqueidentifier:uniqueIdentifier}};return _ajax.default.call([request])[0]};_exports.deleteCondition=(reportId,conditionId)=>{const request={methodname:"core_reportbuilder_conditions_delete",args:{reportid:reportId,conditionid:conditionId}};return _ajax.default.call([request])[0]};_exports.reorderCondition=(reportId,conditionId,position)=>{const request={methodname:"core_reportbuilder_conditions_reorder",args:{reportid:reportId,conditionid:conditionId,position:position}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=conditions.min.js.map