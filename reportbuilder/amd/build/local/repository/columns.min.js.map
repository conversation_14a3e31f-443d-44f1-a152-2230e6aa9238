{"version": 3, "file": "columns.min.js", "sources": ["../../../src/local/repository/columns.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle column AJAX requests\n *\n * @module      core_reportbuilder/local/repository/columns\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Add column to given report\n *\n * @param {Number} reportId\n * @param {String} uniqueIdentifier\n * @return {Promise}\n */\nexport const addColumn = (reportId, uniqueIdentifier) => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_add',\n        args: {reportid: reportId, uniqueidentifier: uniqueIdentifier}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Remove column from given report\n *\n * @param {Number} reportId\n * @param {Number} columnId\n * @return {Promise}\n */\nexport const deleteColumn = (reportId, columnId) => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_delete',\n        args: {reportid: reportId, columnid: columnId}\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Re-order column within a report\n *\n * @param {Number} reportId\n * @param {Number} columnId\n * @param {Number} position\n * @return {Promise}\n */\nexport const reorderColumn = (reportId, columnId, position) => {\n    const request = {\n        methodname: 'core_reportbuilder_columns_reorder',\n        args: {reportid: reportId, columnid: columnId, position: position}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["reportId", "uniqueIdentifier", "request", "methodname", "args", "reportid", "uniqueidentifier", "Ajax", "call", "columnId", "columnid", "position"], "mappings": ";;;;;;;2MAgCyB,CAACA,SAAUC,0BAC1BC,QAAU,CACZC,WAAY,iCACZC,KAAM,CAACC,SAAUL,SAAUM,iBAAkBL,0BAG1CM,cAAKC,KAAK,CAACN,UAAU,0BAUJ,CAACF,SAAUS,kBAC7BP,QAAU,CACZC,WAAY,oCACZC,KAAM,CAACC,SAAUL,SAAUU,SAAUD,kBAGlCF,cAAKC,KAAK,CAACN,UAAU,2BAWH,CAACF,SAAUS,SAAUE,kBACxCT,QAAU,CACZC,WAAY,qCACZC,KAAM,CAACC,SAAUL,SAAUU,SAAUD,SAAUE,SAAUA,kBAGtDJ,cAAKC,KAAK,CAACN,UAAU"}