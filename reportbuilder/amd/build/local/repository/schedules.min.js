define("core_reportbuilder/local/repository/schedules",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle schedule AJAX requests
   *
   * @module      core_reportbuilder/local/repository/schedules
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.toggleSchedule=_exports.sendSchedule=_exports.deleteSchedule=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.deleteSchedule=(reportId,scheduleId)=>{const request={methodname:"core_reportbuilder_schedules_delete",args:{reportid:reportId,scheduleid:scheduleId}};return _ajax.default.call([request])[0]};_exports.sendSchedule=(reportId,scheduleId)=>{const request={methodname:"core_reportbuilder_schedules_send",args:{reportid:reportId,scheduleid:scheduleId}};return _ajax.default.call([request])[0]};_exports.toggleSchedule=(reportId,scheduleId,scheduleEnabled)=>{const request={methodname:"core_reportbuilder_schedules_toggle",args:{reportid:reportId,scheduleid:scheduleId,enabled:scheduleEnabled}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=schedules.min.js.map