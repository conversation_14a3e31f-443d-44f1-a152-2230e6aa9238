define("core_reportbuilder/local/repository/filters",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle filter AJAX requests
   *
   * @module      core_reportbuilder/local/repository/filters
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.setFilters=_exports.resetFilters=_exports.reorderFilter=_exports.deleteFilter=_exports.addFilter=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.resetFilters=(reportId,reportParameters)=>{const request={methodname:"core_reportbuilder_filters_reset",args:{reportid:reportId,parameters:reportParameters}};return _ajax.default.call([request])[0]};_exports.setFilters=(reportId,reportParameters,filterValues)=>{const request={methodname:"core_reportbuilder_set_filters",args:{reportid:reportId,parameters:reportParameters,values:filterValues}};return _ajax.default.call([request])[0]};_exports.addFilter=(reportId,uniqueIdentifier)=>{const request={methodname:"core_reportbuilder_filters_add",args:{reportid:reportId,uniqueidentifier:uniqueIdentifier}};return _ajax.default.call([request])[0]};_exports.deleteFilter=(reportId,filterId)=>{const request={methodname:"core_reportbuilder_filters_delete",args:{reportid:reportId,filterid:filterId}};return _ajax.default.call([request])[0]};_exports.reorderFilter=(reportId,filterId,position)=>{const request={methodname:"core_reportbuilder_filters_reorder",args:{reportid:reportId,filterid:filterId,position:position}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=filters.min.js.map