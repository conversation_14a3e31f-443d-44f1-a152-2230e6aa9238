{"version": 3, "file": "events.min.js", "sources": ["../../src/local/events.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder events\n *\n * @module      core_reportbuilder/local/events\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/**\n * Events for the Report builder subsystem\n *\n * @constant\n * @property {String} tableReload See {@link event:tableReload}\n */\nexport default {\n    /**\n     * Trigger table reloading\n     *\n     * @event tableReload\n     * @type {CustomEvent}\n     * @property {object} detail\n     * @property {Boolean} detail.preservePagination Whether current pagination should be preserved (default false)\n     * @property {String} detail.preserveTriggerElement Element selector that should be focused after table reload (default null)\n     *\n     * @example <caption>Triggering table reload</caption>\n     * import {dispatchEvent} from 'core/event_dispatcher';\n     * import * as reportEvents from 'core_reportbuilder/local/events';\n     *\n     * dispatchEvent(reportEvents.tableReload, {}, document.querySelector(...));\n     */\n    tableReload: 'core_reportbuilder_table_reload',\n    publish: {\n        reportColumnsUpdated: 'core_reportbuilder_report_columns_updated',\n    },\n};\n"], "names": ["tableReload", "publish", "reportColumnsUpdated"], "mappings": "iLA6Be,CAgBXA,YAAa,kCACbC,QAAS,CACLC,qBAAsB"}