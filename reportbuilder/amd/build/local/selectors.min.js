define("core_reportbuilder/local/selectors",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;
/**
   * Report builder selectors
   *
   * @module      core_reportbuilder/local/selectors
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
const SELECTORS={regions:{report:'[data-region="core_reportbuilder/report"]',reportTable:'[data-region="reportbuilder-table"]',columnHeader:'[data-region="column-header"]',filterButtonLabel:'[data-region="filter-button-label"]',filtersForm:'[data-region="filters-form"]',sidebarMenu:'[data-region="sidebar-menu"]',sidebarCard:'[data-region="sidebar-card"]',sidebarItem:'[data-region="sidebar-item"]',settingsConditions:'[data-region="settings-conditions"]',activeConditions:'[data-region="active-conditions"]',activeCondition:'[data-region="active-condition"]',settingsFilters:'[data-region="settings-filters"]',activeFilters:'[data-region="active-filters"]',activeFilter:'[data-region="active-filter"]',settingsSorting:'[data-region="settings-sorting"]',audiencesContainer:'[data-region="audiences"]',audienceFormContainer:'[data-region="audience-form-container"]',audienceCard:'[data-region="audience-card"]',audienceHeading:'[data-region="audience-heading"]',audienceForm:'[data-region="audience-form"]',audienceEmptyMessage:"[data-region=no-instances-message]",audienceDescription:"[data-region=audience-description]",audienceNotSavedLabel:"[data-region=audience-not-saved]",settingsCardView:'[data-region="settings-cardview"]'},actions:{reportActionPopup:'[data-action="report-action-popup"]',reportCreate:'[data-action="report-create"]',reportEdit:'[data-action="report-edit"]',reportDelete:'[data-action="report-delete"]',reportAddColumn:'[data-action="report-add-column"]',reportRemoveColumn:'[data-action="report-remove-column"]',reportAddCondition:'[data-action="report-add-condition"]',reportRemoveCondition:'[data-action="report-remove-condition"]',reportAddFilter:'[data-action="report-add-filter"]',reportRemoveFilter:'[data-action="report-remove-filter"]',reportToggleColumnSort:'[data-action="report-toggle-column-sorting"]',reportToggleColumnSortDirection:'[data-action="report-toggle-sort-direction"]',sidebarSearch:'[data-action="sidebar-search"]',toggleEditPreview:'[data-action="toggle-edit-preview"]',audienceAdd:'[data-action="add-audience"]',audienceEdit:'[data-action="edit-audience"]',audienceDelete:'[data-action="delete-audience"]',toggleCardView:'[data-action="toggle-card"]',scheduleCreate:'[data-action="schedule-create"]',scheduleToggle:'[data-action="schedule-toggle"]',scheduleEdit:'[data-action="schedule-edit"]',scheduleSend:'[data-action="schedule-send"]',scheduleDelete:'[data-action="schedule-delete"]'},forReport:reportId=>"".concat(SELECTORS.regions.report,'[data-report-id="').concat(reportId,'"]')};var _default=SELECTORS;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=selectors.min.js.map