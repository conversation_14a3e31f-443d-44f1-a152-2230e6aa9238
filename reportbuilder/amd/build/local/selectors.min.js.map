{"version": 3, "file": "selectors.min.js", "sources": ["../../src/local/selectors.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder selectors\n *\n * @module      core_reportbuilder/local/selectors\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/**\n * Selectors for the Report builder subsystem\n *\n * @property {Object} regions\n * @property {String} regions.systemReport System report page region\n * @property {String} regions.filterButtonLabel Filters form toggle region\n * @property {String} regions.filtersForm Filters form page region\n */\nconst SELECTORS = {\n    regions: {\n        report: '[data-region=\"core_reportbuilder/report\"]',\n        reportTable: '[data-region=\"reportbuilder-table\"]',\n        columnHeader: '[data-region=\"column-header\"]',\n        filterButtonLabel: '[data-region=\"filter-button-label\"]',\n        filtersForm: '[data-region=\"filters-form\"]',\n        sidebarMenu: '[data-region=\"sidebar-menu\"]',\n        sidebarCard: '[data-region=\"sidebar-card\"]',\n        sidebarItem: '[data-region=\"sidebar-item\"]',\n        settingsConditions: '[data-region=\"settings-conditions\"]',\n        activeConditions: '[data-region=\"active-conditions\"]',\n        activeCondition: '[data-region=\"active-condition\"]',\n        settingsFilters: '[data-region=\"settings-filters\"]',\n        activeFilters: '[data-region=\"active-filters\"]',\n        activeFilter: '[data-region=\"active-filter\"]',\n        settingsSorting: '[data-region=\"settings-sorting\"]',\n        audiencesContainer: '[data-region=\"audiences\"]',\n        audienceFormContainer: '[data-region=\"audience-form-container\"]',\n        audienceCard: '[data-region=\"audience-card\"]',\n        audienceHeading: '[data-region=\"audience-heading\"]',\n        audienceForm: '[data-region=\"audience-form\"]',\n        audienceEmptyMessage: '[data-region=no-instances-message]',\n        audienceDescription: '[data-region=audience-description]',\n        audienceNotSavedLabel: '[data-region=audience-not-saved]',\n        settingsCardView: '[data-region=\"settings-cardview\"]',\n    },\n    actions: {\n        reportActionPopup: '[data-action=\"report-action-popup\"]',\n        reportCreate: '[data-action=\"report-create\"]',\n        reportEdit: '[data-action=\"report-edit\"]',\n        reportDelete: '[data-action=\"report-delete\"]',\n        reportAddColumn: '[data-action=\"report-add-column\"]',\n        reportRemoveColumn: '[data-action=\"report-remove-column\"]',\n        reportAddCondition: '[data-action=\"report-add-condition\"]',\n        reportRemoveCondition: '[data-action=\"report-remove-condition\"]',\n        reportAddFilter: '[data-action=\"report-add-filter\"]',\n        reportRemoveFilter: '[data-action=\"report-remove-filter\"]',\n        reportToggleColumnSort: '[data-action=\"report-toggle-column-sorting\"]',\n        reportToggleColumnSortDirection: '[data-action=\"report-toggle-sort-direction\"]',\n        sidebarSearch: '[data-action=\"sidebar-search\"]',\n        toggleEditPreview: '[data-action=\"toggle-edit-preview\"]',\n        audienceAdd: '[data-action=\"add-audience\"]',\n        audienceEdit: '[data-action=\"edit-audience\"]',\n        audienceDelete: '[data-action=\"delete-audience\"]',\n        toggleCardView: '[data-action=\"toggle-card\"]',\n        scheduleCreate: '[data-action=\"schedule-create\"]',\n        scheduleToggle: '[data-action=\"schedule-toggle\"]',\n        scheduleEdit: '[data-action=\"schedule-edit\"]',\n        scheduleSend: '[data-action=\"schedule-send\"]',\n        scheduleDelete: '[data-action=\"schedule-delete\"]',\n    },\n};\n\n/**\n * Selector for given report\n *\n * @method forReport\n * @param {Number} reportId\n * @return {String}\n */\nSELECTORS.forReport = reportId => `${SELECTORS.regions.report}[data-report-id=\"${reportId}\"]`;\n\nexport default SELECTORS;\n"], "names": ["SELECTORS", "regions", "report", "reportTable", "columnHeader", "filterButtonLabel", "filtersForm", "sidebarMenu", "sidebarCard", "sidebarItem", "settingsConditions", "activeConditions", "activeCondition", "settingsFilters", "activeFilters", "activeFilter", "settingsSorting", "audiencesContainer", "audienceFormContainer", "audienceCard", "audienceHeading", "audienceForm", "audienceEmptyMessage", "audienceDescription", "audienceNotSavedLabel", "settingsCardView", "actions", "reportActionPopup", "reportCreate", "reportEdit", "reportDelete", "reportAddColumn", "reportRemoveColumn", "reportAddCondition", "reportRemoveCondition", "reportAdd<PERSON><PERSON><PERSON>", "reportRemoveFilter", "reportToggleColumnSort", "reportToggleColumnSortDirection", "sidebarSearch", "toggleEditPreview", "audienceAdd", "audienceEdit", "audienceDelete", "toggleCardView", "scheduleCreate", "scheduleToggle", "scheduleEdit", "scheduleSend", "scheduleDelete", "reportId"], "mappings": ";;;;;;;;MA+BMA,UAAY,CACdC,QAAS,CACLC,OAAQ,4CACRC,YAAa,sCACbC,aAAc,gCACdC,kBAAmB,sCACnBC,YAAa,+BACbC,YAAa,+BACbC,YAAa,+BACbC,YAAa,+BACbC,mBAAoB,sCACpBC,iBAAkB,oCAClBC,gBAAiB,mCACjBC,gBAAiB,mCACjBC,cAAe,iCACfC,aAAc,gCACdC,gBAAiB,mCACjBC,mBAAoB,4BACpBC,sBAAuB,0CACvBC,aAAc,gCACdC,gBAAiB,mCACjBC,aAAc,gCACdC,qBAAsB,qCACtBC,oBAAqB,qCACrBC,sBAAuB,mCACvBC,iBAAkB,qCAEtBC,QAAS,CACLC,kBAAmB,sCACnBC,aAAc,gCACdC,WAAY,8BACZC,aAAc,gCACdC,gBAAiB,oCACjBC,mBAAoB,uCACpBC,mBAAoB,uCACpBC,sBAAuB,0CACvBC,gBAAiB,oCACjBC,mBAAoB,uCACpBC,uBAAwB,+CACxBC,gCAAiC,+CACjCC,cAAe,iCACfC,kBAAmB,sCACnBC,YAAa,+BACbC,aAAc,gCACdC,eAAgB,kCAChBC,eAAgB,8BAChBC,eAAgB,kCAChBC,eAAgB,kCAChBC,aAAc,gCACdC,aAAc,gCACdC,eAAgB,mCAWxBjD,UAAsBkD,oBAAelD,UAAUC,QAAQC,mCAA0BgD,6BAElElD"}