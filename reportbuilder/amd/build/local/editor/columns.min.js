define("core_reportbuilder/local/editor/columns",["exports","jquery","core/event_dispatcher","core/inplace_editable","core/local/inplace_editable/events","core/notification","core/pending","core/prefetch","core/pubsub","core/sortable_list","core/str","core/toast","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/columns","core_reportbuilder/local/repository/sorting"],(function(_exports,_jquery,_event_dispatcher,_inplace_editable,_events,_notification,_pending,_prefetch,_pubsub,_sortable_list,_str,_toast,reportEvents,reportSelectors,_columns,_sorting){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_sortable_list=_interopRequireDefault(_sortable_list),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);_exports.init=initialized=>{((0,_prefetch.prefetchStrings)("core_reportbuilder",["columnadded","columnaggregated","columndeleted","columnmoved","deletecolumn","deletecolumnconfirm"]),(0,_prefetch.prefetchStrings)("core",["delete"]),initialized)||(document.addEventListener("click",(event=>{const reportAddColumn=event.target.closest(reportSelectors.actions.reportAddColumn);if(reportAddColumn){event.preventDefault();const pendingPromise=new _pending.default("core_reportbuilder/columns:add"),reportElement=reportAddColumn.closest(reportSelectors.regions.report);(0,_columns.addColumn)(reportElement.dataset.reportId,reportAddColumn.dataset.uniqueIdentifier).then((data=>(0,_pubsub.publish)(reportEvents.publish.reportColumnsUpdated,data))).then((()=>(0,_str.get_string)("columnadded","core_reportbuilder",reportAddColumn.dataset.name))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)}const reportRemoveColumn=event.target.closest(reportSelectors.actions.reportRemoveColumn);if(reportRemoveColumn){event.preventDefault();const reportElement=reportRemoveColumn.closest(reportSelectors.regions.report),columnHeader=reportRemoveColumn.closest(reportSelectors.regions.columnHeader),columnName=columnHeader.dataset.columnName;_notification.default.saveCancelPromise((0,_str.get_string)("deletecolumn","core_reportbuilder",columnName),(0,_str.get_string)("deletecolumnconfirm","core_reportbuilder",columnName),(0,_str.get_string)("delete","core"),{triggerElement:reportRemoveColumn}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/columns:remove");return(0,_columns.deleteColumn)(reportElement.dataset.reportId,columnHeader.dataset.columnId).then((data=>(0,_pubsub.publish)(reportEvents.publish.reportColumnsUpdated,data))).then((()=>(0,_toast.add)((0,_str.get_string)("columndeleted","core_reportbuilder",columnName)))).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))}})),new _sortable_list.default("".concat(reportSelectors.regions.reportTable," thead tr"),{isHorizontal:!0}).getElementName=element=>Promise.resolve(element.data("columnName")),(0,_jquery.default)(document).on(_sortable_list.default.EVENTS.DRAG,"".concat(reportSelectors.regions.report," th[data-column-id]"),((event,info)=>{const reportElement=event.target.closest(reportSelectors.regions.report),columnPosition=info.element.data("columnPosition"),targetColumnPosition=info.targetNextElement.data("columnPosition");(0,_jquery.default)(reportElement).find("tbody tr").each((function(){const cell=(0,_jquery.default)(this).children("td.c".concat(columnPosition-1))[0];if(targetColumnPosition){var beforeCell=(0,_jquery.default)(this).children("td.c".concat(targetColumnPosition-1))[0];this.insertBefore(cell,beforeCell)}else this.appendChild(cell)}))})),(0,_jquery.default)(document).on(_sortable_list.default.EVENTS.DROP,"".concat(reportSelectors.regions.report," th[data-column-id]"),((event,info)=>{if(info.positionChanged){const pendingPromise=new _pending.default("core_reportbuilder/columns:reorder"),reportElement=event.target.closest(reportSelectors.regions.report),columnId=info.element.data("columnId"),columnName=info.element.data("columnName"),columnPosition=info.element.data("columnPosition");let targetColumnPosition=info.targetNextElement.data("columnPosition")||info.element.siblings().length+2;targetColumnPosition>columnPosition&&targetColumnPosition--,(0,_columns.reorderColumn)(reportElement.dataset.reportId,columnId,targetColumnPosition).then((()=>(0,_str.get_string)("columnmoved","core_reportbuilder",columnName))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)}})),document.addEventListener(_events.eventTypes.elementUpdated,(event=>{const columnAggregation=event.target.closest('[data-itemtype="columnaggregation"]');if(columnAggregation){const pendingPromise=new _pending.default("core_reportbuilder/columns:aggregate"),reportElement=columnAggregation.closest(reportSelectors.regions.report),columnHeader=columnAggregation.closest(reportSelectors.regions.columnHeader);(0,_str.get_string)("columnaggregated","core_reportbuilder",columnHeader.dataset.columnName).then(_toast.add).then((()=>{const columnAggregationLink='[data-itemtype="columnaggregation"][data-itemid="'+"".concat(columnAggregation.dataset.itemid,'"] > a');return(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preserveTriggerElement:columnAggregationLink},reportElement),(0,_sorting.getColumnSorting)(reportElement.dataset.reportId)})).then((data=>(0,_pubsub.publish)(reportEvents.publish.reportColumnsUpdated,data))).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)}})))}}));

//# sourceMappingURL=columns.min.js.map