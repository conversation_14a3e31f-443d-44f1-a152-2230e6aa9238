{"version": 3, "file": "filters.min.js", "sources": ["../../../src/local/editor/filters.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder filters editor\n *\n * @module      core_reportbuilder/local/editor/filters\n * @copyright   2021 David Matamoros <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport 'core/inplace_editable';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport SortableList from 'core/sortable_list';\nimport {get_string as getString} from 'core/str';\nimport Templates from 'core/templates';\nimport {add as addToast} from 'core/toast';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {addFilter, deleteFilter, reorderFilter} from 'core_reportbuilder/local/repository/filters';\n\n/**\n * Reload filters settings region\n *\n * @param {Element} reportElement\n * @param {Object} templateContext\n * @return {Promise}\n */\nconst reloadSettingsFiltersRegion = (reportElement, templateContext) => {\n    const pendingPromise = new Pending('core_reportbuilder/filters:reload');\n    const settingsFiltersRegion = reportElement.querySelector(reportSelectors.regions.settingsFilters);\n\n    return Templates.renderForPromise('core_reportbuilder/local/settings/filters', {filters: templateContext})\n        .then(({html, js}) => {\n            Templates.replaceNode(settingsFiltersRegion, html, js);\n\n            initFiltersForm();\n\n            // Re-focus the add filter element after reloading the region.\n            const reportAddFilter = reportElement.querySelector(reportSelectors.actions.reportAddFilter);\n            reportAddFilter?.focus();\n\n            return pendingPromise.resolve();\n        });\n};\n\n/**\n * Initialise filters form, must be called on each init because the form container is re-created when switching editor modes\n */\nconst initFiltersForm = () => {\n    CustomEvents.define(reportSelectors.actions.reportAddFilter, [CustomEvents.events.accessibleChange]);\n};\n\n/**\n * Initialise module, prefetch all required strings\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = initialized => {\n    prefetchStrings('core_reportbuilder', [\n        'deletefilter',\n        'deletefilterconfirm',\n        'filteradded',\n        'filterdeleted',\n        'filtermoved',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    initFiltersForm();\n    if (initialized) {\n        return;\n    }\n\n    // Add filter to report. Use custom events helper to ensure consistency across platforms.\n    $(document).on(CustomEvents.events.accessibleChange, reportSelectors.actions.reportAddFilter, event => {\n        const reportAddFilter = event.target.closest(reportSelectors.actions.reportAddFilter);\n        if (reportAddFilter) {\n            event.preventDefault();\n\n            // Check if dropdown is closed with no filter selected.\n            if (reportAddFilter.selectedIndex === 0) {\n                return;\n            }\n\n            const reportElement = reportAddFilter.closest(reportSelectors.regions.report);\n            const pendingPromise = new Pending('core_reportbuilder/filters:add');\n\n            addFilter(reportElement.dataset.reportId, reportAddFilter.value)\n                .then(data => reloadSettingsFiltersRegion(reportElement, data))\n                .then(() => getString('filteradded', 'core_reportbuilder',\n                    reportAddFilter.options[reportAddFilter.selectedIndex].text))\n                .then(addToast)\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n\n    document.addEventListener('click', event => {\n\n        // Remove filter from report.\n        const reportRemoveFilter = event.target.closest(reportSelectors.actions.reportRemoveFilter);\n        if (reportRemoveFilter) {\n            event.preventDefault();\n\n            const reportElement = reportRemoveFilter.closest(reportSelectors.regions.report);\n            const filterContainer = reportRemoveFilter.closest(reportSelectors.regions.activeFilter);\n            const filterName = filterContainer.dataset.filterName;\n\n            Notification.saveCancelPromise(\n                getString('deletefilter', 'core_reportbuilder', filterName),\n                getString('deletefilterconfirm', 'core_reportbuilder', filterName),\n                getString('delete', 'core'),\n                {triggerElement: reportRemoveFilter}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/filters:remove');\n\n                return deleteFilter(reportElement.dataset.reportId, filterContainer.dataset.filterId)\n                    .then(data => reloadSettingsFiltersRegion(reportElement, data))\n                    .then(() => addToast(getString('filterdeleted', 'core_reportbuilder', filterName)))\n                    .then(() => pendingPromise.resolve())\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n\n    // Initialize sortable list to handle active filters moving (note JQuery dependency, see MDL-72293 for resolution).\n    var activeFiltersSortableList = new SortableList(`${reportSelectors.regions.activeFilters} ul`, {isHorizontal: false});\n    activeFiltersSortableList.getElementName = element => Promise.resolve(element.data('filterName'));\n\n    $(document).on(SortableList.EVENTS.DROP, `${reportSelectors.regions.report} li[data-filter-id]`, (event, info) => {\n        if (info.positionChanged) {\n            const pendingPromise = new Pending('core_reportbuilder/filters:reorder');\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const filterId = info.element.data('filterId');\n            const filterPosition = info.element.data('filterPosition');\n\n            // Select target position, if moving to the end then count number of element siblings.\n            let targetFilterPosition = info.targetNextElement.data('filterPosition') || info.element.siblings().length + 2;\n            if (targetFilterPosition > filterPosition) {\n                targetFilterPosition--;\n            }\n\n            reorderFilter(reportElement.dataset.reportId, filterId, targetFilterPosition)\n                .then(data => reloadSettingsFiltersRegion(reportElement, data))\n                .then(() => getString('filtermoved', 'core_reportbuilder', info.element.data('filterName')))\n                .then(addToast)\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n};\n"], "names": ["reloadSettingsFiltersRegion", "reportElement", "templateContext", "pendingPromise", "Pending", "settingsFiltersRegion", "querySelector", "reportSelectors", "regions", "settingsFilters", "Templates", "renderForPromise", "filters", "then", "_ref", "html", "js", "replaceNode", "initFiltersForm", "reportAdd<PERSON><PERSON><PERSON>", "actions", "focus", "resolve", "define", "CustomEvents", "events", "accessibleChange", "initialized", "document", "on", "event", "target", "closest", "preventDefault", "selectedIndex", "report", "dataset", "reportId", "value", "data", "options", "text", "addToast", "catch", "Notification", "exception", "addEventListener", "reportRemoveFilter", "filterContainer", "activeFilter", "filterName", "saveCancelPromise", "triggerElement", "filterId", "SortableList", "activeFilters", "isHorizontal", "getElementName", "element", "Promise", "EVENTS", "DROP", "info", "positionChanged", "filterPosition", "targetFilterPosition", "targetNextElement", "siblings", "length"], "mappings": "04DA6CMA,4BAA8B,CAACC,cAAeC,yBAC1CC,eAAiB,IAAIC,iBAAQ,qCAC7BC,sBAAwBJ,cAAcK,cAAcC,gBAAgBC,QAAQC,wBAE3EC,mBAAUC,iBAAiB,4CAA6C,CAACC,QAASV,kBACpFW,MAAKC,WAACC,KAACA,KAADC,GAAOA,4BACAC,YAAYZ,sBAAuBU,KAAMC,IAEnDE,wBAGMC,gBAAkBlB,cAAcK,cAAcC,gBAAgBa,QAAQD,wBAC5EA,MAAAA,iBAAAA,gBAAiBE,QAEVlB,eAAemB,cAO5BJ,gBAAkB,wCACPK,OAAOhB,gBAAgBa,QAAQD,gBAAiB,CAACK,mCAAaC,OAAOC,kCAQlEC,6CACA,qBAAsB,CAClC,eACA,sBACA,cACA,gBACA,8CAGY,OAAQ,CACpB,WAGJT,kBACIS,mCAKFC,UAAUC,GAAGL,mCAAaC,OAAOC,iBAAkBnB,gBAAgBa,QAAQD,iBAAiBW,cACpFX,gBAAkBW,MAAMC,OAAOC,QAAQzB,gBAAgBa,QAAQD,oBACjEA,gBAAiB,IACjBW,MAAMG,iBAGgC,IAAlCd,gBAAgBe,2BAIdjC,cAAgBkB,gBAAgBa,QAAQzB,gBAAgBC,QAAQ2B,QAChEhC,eAAiB,IAAIC,iBAAQ,yDAEzBH,cAAcmC,QAAQC,SAAUlB,gBAAgBmB,OACrDzB,MAAK0B,MAAQvC,4BAA4BC,cAAesC,QACxD1B,MAAK,KAAM,mBAAU,cAAe,qBACjCM,gBAAgBqB,QAAQrB,gBAAgBe,eAAeO,QAC1D5B,KAAK6B,YACL7B,MAAK,IAAMV,eAAemB,YAC1BqB,MAAMC,sBAAaC,eAIhCjB,SAASkB,iBAAiB,SAAShB,cAGzBiB,mBAAqBjB,MAAMC,OAAOC,QAAQzB,gBAAgBa,QAAQ2B,uBACpEA,mBAAoB,CACpBjB,MAAMG,uBAEAhC,cAAgB8C,mBAAmBf,QAAQzB,gBAAgBC,QAAQ2B,QACnEa,gBAAkBD,mBAAmBf,QAAQzB,gBAAgBC,QAAQyC,cACrEC,WAAaF,gBAAgBZ,QAAQc,iCAE9BC,mBACT,mBAAU,eAAgB,qBAAsBD,aAChD,mBAAU,sBAAuB,qBAAsBA,aACvD,mBAAU,SAAU,QACpB,CAACE,eAAgBL,qBACnBlC,MAAK,WACGV,eAAiB,IAAIC,iBAAQ,4CAE5B,yBAAaH,cAAcmC,QAAQC,SAAUW,gBAAgBZ,QAAQiB,UACvExC,MAAK0B,MAAQvC,4BAA4BC,cAAesC,QACxD1B,MAAK,KAAM,eAAS,mBAAU,gBAAiB,qBAAsBqC,eACrErC,MAAK,IAAMV,eAAemB,YAC1BqB,MAAMC,sBAAaC,cACzBF,OAAM,aAOe,IAAIW,iCAAgB/C,gBAAgBC,QAAQ+C,qBAAoB,CAACC,cAAc,IACrFC,eAAiBC,SAAWC,QAAQrC,QAAQoC,QAAQnB,KAAK,mCAEjFX,UAAUC,GAAGyB,uBAAaM,OAAOC,eAAStD,gBAAgBC,QAAQ2B,+BAA6B,CAACL,MAAOgC,WACjGA,KAAKC,gBAAiB,OAChB5D,eAAiB,IAAIC,iBAAQ,sCAC7BH,cAAgB6B,MAAMC,OAAOC,QAAQzB,gBAAgBC,QAAQ2B,QAC7DkB,SAAWS,KAAKJ,QAAQnB,KAAK,YAC7ByB,eAAiBF,KAAKJ,QAAQnB,KAAK,sBAGrC0B,qBAAuBH,KAAKI,kBAAkB3B,KAAK,mBAAqBuB,KAAKJ,QAAQS,WAAWC,OAAS,EACzGH,qBAAuBD,gBACvBC,kDAGUhE,cAAcmC,QAAQC,SAAUgB,SAAUY,sBACnDpD,MAAK0B,MAAQvC,4BAA4BC,cAAesC,QACxD1B,MAAK,KAAM,mBAAU,cAAe,qBAAsBiD,KAAKJ,QAAQnB,KAAK,iBAC5E1B,KAAK6B,YACL7B,MAAK,IAAMV,eAAemB,YAC1BqB,MAAMC,sBAAaC"}