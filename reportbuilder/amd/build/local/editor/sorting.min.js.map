{"version": 3, "file": "sorting.min.js", "sources": ["../../../src/local/editor/sorting.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder columns sorting editor\n *\n * @module      core_reportbuilder/local/editor/sorting\n * @copyright   2021 David Matamoros <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport 'core/inplace_editable';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {subscribe} from 'core/pubsub';\nimport SortableList from 'core/sortable_list';\nimport {get_string as getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {reorderColumnSorting, toggleColumnSorting} from 'core_reportbuilder/local/repository/sorting';\nimport Templates from 'core/templates';\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport * as reportEvents from 'core_reportbuilder/local/events';\n\n// These constants match PHP consts SORT_ASC, SORT_DESC.\nconst SORTORDER = {\n    ASCENDING: 4,\n    DESCENDING: 3,\n};\n\n/**\n * Reload sorting settings region\n *\n * @param {Object} context\n * @return {Promise}\n */\nconst reloadSettingsSortingRegion = context => {\n    const pendingPromise = new Pending('core_reportbuilder/sorting:reload');\n    const settingsSortingRegion = document.querySelector(reportSelectors.regions.settingsSorting);\n\n    return Templates.renderForPromise('core_reportbuilder/local/settings/sorting', {sorting: context})\n        .then(({html, js}) => {\n            Templates.replaceNode(settingsSortingRegion, html, js);\n            return pendingPromise.resolve();\n        });\n};\n\n/**\n * Updates column sorting\n *\n * @param {Element} reportElement\n * @param {Element} element\n * @param {Number} sortenabled\n * @param {Number} sortdirection\n * @return {Promise}\n */\nconst updateSorting = (reportElement, element, sortenabled, sortdirection) => {\n    const reportId = reportElement.dataset.reportId;\n    const listElement = element.closest('li');\n    const columnId = listElement.dataset.columnSortId;\n    const columnName = listElement.dataset.columnSortName;\n\n    return toggleColumnSorting(reportId, columnId, sortenabled, sortdirection)\n        .then(reloadSettingsSortingRegion)\n        .then(() => getString('columnsortupdated', 'core_reportbuilder', columnName))\n        .then(addToast)\n        .then(() => {\n            dispatchEvent(reportEvents.tableReload, {}, reportElement);\n            return null;\n        });\n};\n\n/**\n * Initialise module\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = (initialized) => {\n    if (initialized) {\n        return;\n    }\n\n    // Update sorting region each time report columns are updated (added or removed).\n    subscribe(reportEvents.publish.reportColumnsUpdated, data => reloadSettingsSortingRegion(data)\n        .catch(Notification.exception)\n    );\n\n    document.addEventListener('click', event => {\n\n        // Enable/disable sorting on columns.\n        const toggleSorting = event.target.closest(reportSelectors.actions.reportToggleColumnSort);\n        if (toggleSorting) {\n            event.preventDefault();\n\n            const pendingPromise = new Pending('core_reportbuilder/sorting:toggle');\n            const reportElement = toggleSorting.closest(reportSelectors.regions.report);\n            const sortdirection = parseInt(toggleSorting.closest('li').dataset.columnSortDirection);\n\n            updateSorting(reportElement, toggleSorting, toggleSorting.checked, sortdirection)\n                .then(() => {\n                    // Re-focus the toggle sorting element after reloading the region.\n                    const toggleSortingElement = document.getElementById(toggleSorting.id);\n                    toggleSortingElement?.focus();\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n\n        // Change column sort direction.\n        const toggleSortDirection = event.target.closest(reportSelectors.actions.reportToggleColumnSortDirection);\n        if (toggleSortDirection) {\n            event.preventDefault();\n\n            const pendingPromise = new Pending('core_reportbuilder/sorting:direction');\n            const reportElement = toggleSortDirection.closest(reportSelectors.regions.report);\n            const listElement = toggleSortDirection.closest('li');\n            const toggleSorting = listElement.querySelector(reportSelectors.actions.reportToggleColumnSort);\n\n            let sortdirection = parseInt(listElement.dataset.columnSortDirection);\n            if (sortdirection === SORTORDER.ASCENDING) {\n                sortdirection = SORTORDER.DESCENDING;\n            } else if (sortdirection === SORTORDER.DESCENDING) {\n                sortdirection = SORTORDER.ASCENDING;\n            }\n\n            updateSorting(reportElement, toggleSortDirection, toggleSorting.checked, sortdirection)\n                .then(() => {\n                    // Re-focus the toggle sort direction element after reloading the region.\n                    const toggleSortDirectionElement = document.getElementById(toggleSortDirection.id);\n                    toggleSortDirectionElement?.focus();\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n    });\n\n    // Initialize sortable list to handle column sorting moving (note JQuery dependency, see MDL-72293 for resolution).\n    var columnsSortingSortableList = new SortableList(`${reportSelectors.regions.settingsSorting} ul`, {isHorizontal: false});\n    columnsSortingSortableList.getElementName = element => Promise.resolve(element.data('columnSortName'));\n\n    $(document).on(SortableList.EVENTS.DROP, `${reportSelectors.regions.report} li[data-column-sort-id]`, (event, info) => {\n        if (info.positionChanged) {\n            const pendingPromise = new Pending('core_reportbuilder/sorting:reorder');\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const columnId = info.element.data('columnSortId');\n            const columnPosition = info.element.data('columnSortPosition');\n\n            // Select target position, if moving to the end then count number of element siblings.\n            let targetColumnSortPosition = info.targetNextElement.data('columnSortPosition') || info.element.siblings().length + 2;\n            if (targetColumnSortPosition > columnPosition) {\n                targetColumnSortPosition--;\n            }\n\n            reorderColumnSorting(reportElement.dataset.reportId, columnId, targetColumnSortPosition)\n                .then(reloadSettingsSortingRegion)\n                .then(() => getString('columnsortupdated', 'core_reportbuilder', info.element.data('columnSortName')))\n                .then(addToast)\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                    return null;\n                })\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n};\n"], "names": ["SORTORDER", "reloadSettingsSortingRegion", "context", "pendingPromise", "Pending", "settingsSortingRegion", "document", "querySelector", "reportSelectors", "regions", "settingsSorting", "Templates", "renderForPromise", "sorting", "then", "_ref", "html", "js", "replaceNode", "resolve", "updateSorting", "reportElement", "element", "sortenabled", "sortdirection", "reportId", "dataset", "listElement", "closest", "columnId", "columnSortId", "columnName", "columnSortName", "addToast", "reportEvents", "tableReload", "initialized", "publish", "reportColumnsUpdated", "data", "catch", "Notification", "exception", "addEventListener", "event", "toggleSorting", "target", "actions", "reportToggleColumnSort", "preventDefault", "report", "parseInt", "columnSortDirection", "checked", "toggleSortingElement", "getElementById", "id", "focus", "toggleSortDirection", "reportToggleColumnSortDirection", "toggleSortDirectionElement", "SortableList", "isHorizontal", "getElementName", "Promise", "on", "EVENTS", "DROP", "info", "positionChanged", "columnPosition", "targetColumnSortPosition", "targetNextElement", "siblings", "length"], "mappings": "u7DAwCMA,oBACS,EADTA,qBAEU,EASVC,4BAA8BC,gBAC1BC,eAAiB,IAAIC,iBAAQ,qCAC7BC,sBAAwBC,SAASC,cAAcC,gBAAgBC,QAAQC,wBAEtEC,mBAAUC,iBAAiB,4CAA6C,CAACC,QAASX,UACpFY,MAAKC,WAACC,KAACA,KAADC,GAAOA,mCACAC,YAAYb,sBAAuBW,KAAMC,IAC5Cd,eAAegB,cAa5BC,cAAgB,CAACC,cAAeC,QAASC,YAAaC,uBAClDC,SAAWJ,cAAcK,QAAQD,SACjCE,YAAcL,QAAQM,QAAQ,MAC9BC,SAAWF,YAAYD,QAAQI,aAC/BC,WAAaJ,YAAYD,QAAQM,sBAEhC,gCAAoBP,SAAUI,SAAUN,YAAaC,eACvDV,KAAKb,6BACLa,MAAK,KAAM,mBAAU,oBAAqB,qBAAsBiB,cAChEjB,KAAKmB,YACLnB,MAAK,yCACYoB,aAAaC,YAAa,GAAId,eACrC,uBASEe,cACbA,oCAKMF,aAAaG,QAAQC,sBAAsBC,MAAQtC,4BAA4BsC,MACpFC,MAAMC,sBAAaC,aAGxBpC,SAASqC,iBAAiB,SAASC,cAGzBC,cAAgBD,MAAME,OAAOlB,QAAQpB,gBAAgBuC,QAAQC,2BAC/DH,cAAe,CACfD,MAAMK,uBAEA9C,eAAiB,IAAIC,iBAAQ,qCAC7BiB,cAAgBwB,cAAcjB,QAAQpB,gBAAgBC,QAAQyC,QAC9D1B,cAAgB2B,SAASN,cAAcjB,QAAQ,MAAMF,QAAQ0B,qBAEnEhC,cAAcC,cAAewB,cAAeA,cAAcQ,QAAS7B,eAC9DV,MAAK,WAEIwC,qBAAuBhD,SAASiD,eAAeV,cAAcW,WACnEF,MAAAA,sBAAAA,qBAAsBG,QACftD,eAAegB,aAEzBqB,MAAMC,sBAAaC,iBAItBgB,oBAAsBd,MAAME,OAAOlB,QAAQpB,gBAAgBuC,QAAQY,oCACrED,oBAAqB,CACrBd,MAAMK,uBAEA9C,eAAiB,IAAIC,iBAAQ,wCAC7BiB,cAAgBqC,oBAAoB9B,QAAQpB,gBAAgBC,QAAQyC,QACpEvB,YAAc+B,oBAAoB9B,QAAQ,MAC1CiB,cAAgBlB,YAAYpB,cAAcC,gBAAgBuC,QAAQC,4BAEpExB,cAAgB2B,SAASxB,YAAYD,QAAQ0B,qBAC7C5B,gBAAkBxB,oBAClBwB,cAAgBxB,qBACTwB,gBAAkBxB,uBACzBwB,cAAgBxB,qBAGpBoB,cAAcC,cAAeqC,oBAAqBb,cAAcQ,QAAS7B,eACpEV,MAAK,WAEI8C,2BAA6BtD,SAASiD,eAAeG,oBAAoBF,WAC/EI,MAAAA,4BAAAA,2BAA4BH,QACrBtD,eAAegB,aAEzBqB,MAAMC,sBAAaC,eAKC,IAAImB,iCAAgBrD,gBAAgBC,QAAQC,uBAAsB,CAACoD,cAAc,IACvFC,eAAiBzC,SAAW0C,QAAQ7C,QAAQG,QAAQiB,KAAK,uCAElFjC,UAAU2D,GAAGJ,uBAAaK,OAAOC,eAAS3D,gBAAgBC,QAAQyC,oCAAkC,CAACN,MAAOwB,WACtGA,KAAKC,gBAAiB,OAChBlE,eAAiB,IAAIC,iBAAQ,sCAC7BiB,cAAgBuB,MAAME,OAAOlB,QAAQpB,gBAAgBC,QAAQyC,QAC7DrB,SAAWuC,KAAK9C,QAAQiB,KAAK,gBAC7B+B,eAAiBF,KAAK9C,QAAQiB,KAAK,0BAGrCgC,yBAA2BH,KAAKI,kBAAkBjC,KAAK,uBAAyB6B,KAAK9C,QAAQmD,WAAWC,OAAS,EACjHH,yBAA2BD,gBAC3BC,6DAGiBlD,cAAcK,QAAQD,SAAUI,SAAU0C,0BAC1DzD,KAAKb,6BACLa,MAAK,KAAM,mBAAU,oBAAqB,qBAAsBsD,KAAK9C,QAAQiB,KAAK,qBAClFzB,KAAKmB,YACLnB,MAAK,yCACYoB,aAAaC,YAAa,GAAId,eACrC,QAEVP,MAAK,IAAMX,eAAegB,YAC1BqB,MAAMC,sBAAaC"}