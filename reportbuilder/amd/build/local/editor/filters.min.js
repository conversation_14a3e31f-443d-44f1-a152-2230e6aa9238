define("core_reportbuilder/local/editor/filters",["exports","jquery","core/custom_interaction_events","core/inplace_editable","core/notification","core/pending","core/prefetch","core/sortable_list","core/str","core/templates","core/toast","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/filters"],(function(_exports,_jquery,_custom_interaction_events,_inplace_editable,_notification,_pending,_prefetch,_sortable_list,_str,_templates,_toast,reportSelectors,_filters){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_sortable_list=_interopRequireDefault(_sortable_list),_templates=_interopRequireDefault(_templates),reportSelectors=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(reportSelectors);const reloadSettingsFiltersRegion=(reportElement,templateContext)=>{const pendingPromise=new _pending.default("core_reportbuilder/filters:reload"),settingsFiltersRegion=reportElement.querySelector(reportSelectors.regions.settingsFilters);return _templates.default.renderForPromise("core_reportbuilder/local/settings/filters",{filters:templateContext}).then((_ref=>{let{html:html,js:js}=_ref;_templates.default.replaceNode(settingsFiltersRegion,html,js),initFiltersForm();const reportAddFilter=reportElement.querySelector(reportSelectors.actions.reportAddFilter);return null==reportAddFilter||reportAddFilter.focus(),pendingPromise.resolve()}))},initFiltersForm=()=>{_custom_interaction_events.default.define(reportSelectors.actions.reportAddFilter,[_custom_interaction_events.default.events.accessibleChange])};_exports.init=initialized=>{((0,_prefetch.prefetchStrings)("core_reportbuilder",["deletefilter","deletefilterconfirm","filteradded","filterdeleted","filtermoved"]),(0,_prefetch.prefetchStrings)("core",["delete"]),initFiltersForm(),initialized)||((0,_jquery.default)(document).on(_custom_interaction_events.default.events.accessibleChange,reportSelectors.actions.reportAddFilter,(event=>{const reportAddFilter=event.target.closest(reportSelectors.actions.reportAddFilter);if(reportAddFilter){if(event.preventDefault(),0===reportAddFilter.selectedIndex)return;const reportElement=reportAddFilter.closest(reportSelectors.regions.report),pendingPromise=new _pending.default("core_reportbuilder/filters:add");(0,_filters.addFilter)(reportElement.dataset.reportId,reportAddFilter.value).then((data=>reloadSettingsFiltersRegion(reportElement,data))).then((()=>(0,_str.get_string)("filteradded","core_reportbuilder",reportAddFilter.options[reportAddFilter.selectedIndex].text))).then(_toast.add).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)}})),document.addEventListener("click",(event=>{const reportRemoveFilter=event.target.closest(reportSelectors.actions.reportRemoveFilter);if(reportRemoveFilter){event.preventDefault();const reportElement=reportRemoveFilter.closest(reportSelectors.regions.report),filterContainer=reportRemoveFilter.closest(reportSelectors.regions.activeFilter),filterName=filterContainer.dataset.filterName;_notification.default.saveCancelPromise((0,_str.get_string)("deletefilter","core_reportbuilder",filterName),(0,_str.get_string)("deletefilterconfirm","core_reportbuilder",filterName),(0,_str.get_string)("delete","core"),{triggerElement:reportRemoveFilter}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/filters:remove");return(0,_filters.deleteFilter)(reportElement.dataset.reportId,filterContainer.dataset.filterId).then((data=>reloadSettingsFiltersRegion(reportElement,data))).then((()=>(0,_toast.add)((0,_str.get_string)("filterdeleted","core_reportbuilder",filterName)))).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)})).catch((()=>{}))}})),new _sortable_list.default("".concat(reportSelectors.regions.activeFilters," ul"),{isHorizontal:!1}).getElementName=element=>Promise.resolve(element.data("filterName")),(0,_jquery.default)(document).on(_sortable_list.default.EVENTS.DROP,"".concat(reportSelectors.regions.report," li[data-filter-id]"),((event,info)=>{if(info.positionChanged){const pendingPromise=new _pending.default("core_reportbuilder/filters:reorder"),reportElement=event.target.closest(reportSelectors.regions.report),filterId=info.element.data("filterId"),filterPosition=info.element.data("filterPosition");let targetFilterPosition=info.targetNextElement.data("filterPosition")||info.element.siblings().length+2;targetFilterPosition>filterPosition&&targetFilterPosition--,(0,_filters.reorderFilter)(reportElement.dataset.reportId,filterId,targetFilterPosition).then((data=>reloadSettingsFiltersRegion(reportElement,data))).then((()=>(0,_str.get_string)("filtermoved","core_reportbuilder",info.element.data("filterName")))).then(_toast.add).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)}})))}}));

//# sourceMappingURL=filters.min.js.map