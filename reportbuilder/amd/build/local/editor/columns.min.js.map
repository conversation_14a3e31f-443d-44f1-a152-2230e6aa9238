{"version": 3, "file": "columns.min.js", "sources": ["../../../src/local/editor/columns.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder columns editor\n *\n * @module      core_reportbuilder/local/editor/columns\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport 'core/inplace_editable';\nimport {eventTypes as inplaceEditableEvents} from 'core/local/inplace_editable/events';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport {publish} from 'core/pubsub';\nimport SortableList from 'core/sortable_list';\nimport {get_string as getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {addColumn, deleteColumn, reorderColumn} from 'core_reportbuilder/local/repository/columns';\nimport {getColumnSorting} from 'core_reportbuilder/local/repository/sorting';\n\n/**\n * Initialise module, prefetch all required strings\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = initialized => {\n    prefetchStrings('core_reportbuilder', [\n        'columnadded',\n        'columnaggregated',\n        'columndeleted',\n        'columnmoved',\n        'deletecolumn',\n        'deletecolumnconfirm',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    if (initialized) {\n        return;\n    }\n\n    document.addEventListener('click', event => {\n\n        // Add column to report.\n        const reportAddColumn = event.target.closest(reportSelectors.actions.reportAddColumn);\n        if (reportAddColumn) {\n            event.preventDefault();\n\n            const pendingPromise = new Pending('core_reportbuilder/columns:add');\n            const reportElement = reportAddColumn.closest(reportSelectors.regions.report);\n\n            addColumn(reportElement.dataset.reportId, reportAddColumn.dataset.uniqueIdentifier)\n                .then(data => publish(reportEvents.publish.reportColumnsUpdated, data))\n                .then(() => getString('columnadded', 'core_reportbuilder', reportAddColumn.dataset.name))\n                .then(addToast)\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n\n        // Remove column from report.\n        const reportRemoveColumn = event.target.closest(reportSelectors.actions.reportRemoveColumn);\n        if (reportRemoveColumn) {\n            event.preventDefault();\n\n            const reportElement = reportRemoveColumn.closest(reportSelectors.regions.report);\n            const columnHeader = reportRemoveColumn.closest(reportSelectors.regions.columnHeader);\n            const columnName = columnHeader.dataset.columnName;\n\n            Notification.saveCancelPromise(\n                getString('deletecolumn', 'core_reportbuilder', columnName),\n                getString('deletecolumnconfirm', 'core_reportbuilder', columnName),\n                getString('delete', 'core'),\n                {triggerElement: reportRemoveColumn}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/columns:remove');\n\n                return deleteColumn(reportElement.dataset.reportId, columnHeader.dataset.columnId)\n                    .then(data => publish(reportEvents.publish.reportColumnsUpdated, data))\n                    .then(() => addToast(getString('columndeleted', 'core_reportbuilder', columnName)))\n                    .then(() => {\n                        dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                        return pendingPromise.resolve();\n                    })\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n\n    // Initialize sortable list to handle column moving (note JQuery dependency, see MDL-72293 for resolution).\n    var columnSortableList = new SortableList(`${reportSelectors.regions.reportTable} thead tr`, {isHorizontal: true});\n    columnSortableList.getElementName = element => Promise.resolve(element.data('columnName'));\n\n    $(document).on(SortableList.EVENTS.DRAG, `${reportSelectors.regions.report} th[data-column-id]`, (event, info) => {\n        const reportElement = event.target.closest(reportSelectors.regions.report);\n        const columnPosition = info.element.data('columnPosition');\n        const targetColumnPosition = info.targetNextElement.data('columnPosition');\n\n        $(reportElement).find('tbody tr').each(function() {\n            const cell = $(this).children(`td.c${columnPosition - 1}`)[0];\n            if (targetColumnPosition) {\n                var beforeCell = $(this).children(`td.c${targetColumnPosition - 1}`)[0];\n                this.insertBefore(cell, beforeCell);\n            } else {\n                this.appendChild(cell);\n            }\n        });\n    });\n\n    $(document).on(SortableList.EVENTS.DROP, `${reportSelectors.regions.report} th[data-column-id]`, (event, info) => {\n        if (info.positionChanged) {\n            const pendingPromise = new Pending('core_reportbuilder/columns:reorder');\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const columnId = info.element.data('columnId');\n            const columnName = info.element.data('columnName');\n            const columnPosition = info.element.data('columnPosition');\n\n            // Select target position, if moving to the end then count number of element siblings.\n            let targetColumnPosition = info.targetNextElement.data('columnPosition') || info.element.siblings().length + 2;\n            if (targetColumnPosition > columnPosition) {\n                targetColumnPosition--;\n            }\n\n            reorderColumn(reportElement.dataset.reportId, columnId, targetColumnPosition)\n                .then(() => getString('columnmoved', 'core_reportbuilder', columnName))\n                .then(addToast)\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n    });\n\n    // Initialize inplace editable listeners for column aggregation.\n    document.addEventListener(inplaceEditableEvents.elementUpdated, event => {\n\n        const columnAggregation = event.target.closest('[data-itemtype=\"columnaggregation\"]');\n        if (columnAggregation) {\n            const pendingPromise = new Pending('core_reportbuilder/columns:aggregate');\n            const reportElement = columnAggregation.closest(reportSelectors.regions.report);\n            const columnHeader = columnAggregation.closest(reportSelectors.regions.columnHeader);\n\n            getString('columnaggregated', 'core_reportbuilder', columnHeader.dataset.columnName)\n                .then(addToast)\n                .then(() => {\n                    // Pass preserveTriggerElement parameter so columnAggregationLink will be focused after the report reload.\n                    const columnAggregationLink = `[data-itemtype=\"columnaggregation\"][data-itemid=\"`\n                        + `${columnAggregation.dataset.itemid}\"] > a`;\n\n                    // Now reload the table, and notify listeners that columns have been updated.\n                    dispatchEvent(reportEvents.tableReload, {preserveTriggerElement: columnAggregationLink}, reportElement);\n                    return getColumnSorting(reportElement.dataset.reportId);\n                })\n                .then(data => publish(reportEvents.publish.reportColumnsUpdated, data))\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n};\n"], "names": ["initialized", "document", "addEventListener", "event", "reportAddColumn", "target", "closest", "reportSelectors", "actions", "preventDefault", "pendingPromise", "Pending", "reportElement", "regions", "report", "dataset", "reportId", "uniqueIdentifier", "then", "data", "reportEvents", "publish", "reportColumnsUpdated", "name", "addToast", "tableReload", "preservePagination", "resolve", "catch", "Notification", "exception", "reportRemoveColumn", "columnHeader", "columnName", "saveCancelPromise", "triggerElement", "columnId", "SortableList", "reportTable", "isHorizontal", "getElementName", "element", "Promise", "on", "EVENTS", "DRAG", "info", "columnPosition", "targetColumnPosition", "targetNextElement", "find", "each", "cell", "this", "children", "beforeCell", "insertBefore", "append<PERSON><PERSON><PERSON>", "DROP", "positionChanged", "siblings", "length", "inplaceEditableEvents", "elementUpdated", "columnAggregation", "columnAggregationLink", "itemid", "preserveTriggerElement"], "mappings": "m/DA8CoBA,6CACA,qBAAsB,CAClC,cACA,mBACA,gBACA,cACA,eACA,sDAGY,OAAQ,CACpB,WAGAA,eAIJC,SAASC,iBAAiB,SAASC,cAGzBC,gBAAkBD,MAAME,OAAOC,QAAQC,gBAAgBC,QAAQJ,oBACjEA,gBAAiB,CACjBD,MAAMM,uBAEAC,eAAiB,IAAIC,iBAAQ,kCAC7BC,cAAgBR,gBAAgBE,QAAQC,gBAAgBM,QAAQC,+BAE5DF,cAAcG,QAAQC,SAAUZ,gBAAgBW,QAAQE,kBAC7DC,MAAKC,OAAQ,mBAAQC,aAAaC,QAAQC,qBAAsBH,QAChED,MAAK,KAAM,mBAAU,cAAe,qBAAsBd,gBAAgBW,QAAQQ,QAClFL,KAAKM,YACLN,MAAK,yCACYE,aAAaK,YAAa,CAACC,oBAAoB,GAAOd,eAC7DF,eAAeiB,aAEzBC,MAAMC,sBAAaC,iBAItBC,mBAAqB5B,MAAME,OAAOC,QAAQC,gBAAgBC,QAAQuB,uBACpEA,mBAAoB,CACpB5B,MAAMM,uBAEAG,cAAgBmB,mBAAmBzB,QAAQC,gBAAgBM,QAAQC,QACnEkB,aAAeD,mBAAmBzB,QAAQC,gBAAgBM,QAAQmB,cAClEC,WAAaD,aAAajB,QAAQkB,iCAE3BC,mBACT,mBAAU,eAAgB,qBAAsBD,aAChD,mBAAU,sBAAuB,qBAAsBA,aACvD,mBAAU,SAAU,QACpB,CAACE,eAAgBJ,qBACnBb,MAAK,WACGR,eAAiB,IAAIC,iBAAQ,4CAE5B,yBAAaC,cAAcG,QAAQC,SAAUgB,aAAajB,QAAQqB,UACpElB,MAAKC,OAAQ,mBAAQC,aAAaC,QAAQC,qBAAsBH,QAChED,MAAK,KAAM,eAAS,mBAAU,gBAAiB,qBAAsBe,eACrEf,MAAK,yCACYE,aAAaK,YAAa,CAACC,oBAAoB,GAAOd,eAC7DF,eAAeiB,aAEzBC,MAAMC,sBAAaC,cACzBF,OAAM,aAOQ,IAAIS,iCAAgB9B,gBAAgBM,QAAQyB,yBAAwB,CAACC,cAAc,IACzFC,eAAiBC,SAAWC,QAAQf,QAAQc,QAAQtB,KAAK,mCAE1ElB,UAAU0C,GAAGN,uBAAaO,OAAOC,eAAStC,gBAAgBM,QAAQC,+BAA6B,CAACX,MAAO2C,cAC/FlC,cAAgBT,MAAME,OAAOC,QAAQC,gBAAgBM,QAAQC,QAC7DiC,eAAiBD,KAAKL,QAAQtB,KAAK,kBACnC6B,qBAAuBF,KAAKG,kBAAkB9B,KAAK,sCAEvDP,eAAesC,KAAK,YAAYC,MAAK,iBAC7BC,MAAO,mBAAEC,MAAMC,uBAAgBP,eAAiB,IAAK,MACvDC,qBAAsB,KAClBO,YAAa,mBAAEF,MAAMC,uBAAgBN,qBAAuB,IAAK,QAChEQ,aAAaJ,KAAMG,sBAEnBE,YAAYL,gCAK3BnD,UAAU0C,GAAGN,uBAAaO,OAAOc,eAASnD,gBAAgBM,QAAQC,+BAA6B,CAACX,MAAO2C,WACjGA,KAAKa,gBAAiB,OAChBjD,eAAiB,IAAIC,iBAAQ,sCAC7BC,cAAgBT,MAAME,OAAOC,QAAQC,gBAAgBM,QAAQC,QAC7DsB,SAAWU,KAAKL,QAAQtB,KAAK,YAC7Bc,WAAaa,KAAKL,QAAQtB,KAAK,cAC/B4B,eAAiBD,KAAKL,QAAQtB,KAAK,sBAGrC6B,qBAAuBF,KAAKG,kBAAkB9B,KAAK,mBAAqB2B,KAAKL,QAAQmB,WAAWC,OAAS,EACzGb,qBAAuBD,gBACvBC,kDAGUpC,cAAcG,QAAQC,SAAUoB,SAAUY,sBACnD9B,MAAK,KAAM,mBAAU,cAAe,qBAAsBe,cAC1Df,KAAKM,YACLN,MAAK,yCACYE,aAAaK,YAAa,CAACC,oBAAoB,GAAOd,eAC7DF,eAAeiB,aAEzBC,MAAMC,sBAAaC,eAKhC7B,SAASC,iBAAiB4D,mBAAsBC,gBAAgB5D,cAEtD6D,kBAAoB7D,MAAME,OAAOC,QAAQ,0CAC3C0D,kBAAmB,OACbtD,eAAiB,IAAIC,iBAAQ,wCAC7BC,cAAgBoD,kBAAkB1D,QAAQC,gBAAgBM,QAAQC,QAClEkB,aAAegC,kBAAkB1D,QAAQC,gBAAgBM,QAAQmB,kCAE7D,mBAAoB,qBAAsBA,aAAajB,QAAQkB,YACpEf,KAAKM,YACLN,MAAK,WAEI+C,sBAAwB,8DACrBD,kBAAkBjD,QAAQmD,2DAGrB9C,aAAaK,YAAa,CAAC0C,uBAAwBF,uBAAwBrD,gBAClF,6BAAiBA,cAAcG,QAAQC,aAEjDE,MAAKC,OAAQ,mBAAQC,aAAaC,QAAQC,qBAAsBH,QAChED,MAAK,IAAMR,eAAeiB,YAC1BC,MAAMC,sBAAaC"}