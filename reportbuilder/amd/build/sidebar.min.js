define("core_reportbuilder/sidebar",["exports","core/pending","core/utils","core_reportbuilder/local/selectors"],(function(_exports,_pending,_utils,reportSelectors){var obj;
/**
   * Report builder sidebar component
   *
   * @module      core_reportbuilder/sidebar
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_pending=(obj=_pending)&&obj.__esModule?obj:{default:obj},reportSelectors=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(reportSelectors);const CLASSES_EXPANDED="show",CLASSES_COLLAPSED="collapsed",CLASSES_HIDE="d-none",sidebarCardFilter=(event,sidebarMenu)=>{const pendingPromise=new _pending.default("core_reportbuilder/sidebar:cardFilter"),sidebarCards=sidebarMenu.querySelectorAll(reportSelectors.regions.sidebarCard),sidebarItems=sidebarMenu.querySelectorAll(reportSelectors.regions.sidebarItem),searchTerm=event.target.value.toLowerCase();sidebarItems.forEach((item=>{const itemContent=item.textContent.toLowerCase();item.classList.toggle(CLASSES_HIDE,!itemContent.includes(searchTerm))})),sidebarCards.forEach((card=>{const visibleItems=card.querySelectorAll("".concat(reportSelectors.regions.sidebarItem,":not(.").concat(CLASSES_HIDE,")"));card.classList.toggle(CLASSES_HIDE,!visibleItems.length),expandCard(card)})),pendingPromise.resolve()},expandCard=card=>{let cardButton=card.querySelector('[data-toggle="collapse"]');if(cardButton.classList.contains(CLASSES_COLLAPSED)){cardButton.classList.remove(CLASSES_COLLAPSED),cardButton.setAttribute("aria-expanded","true"),card.querySelector(cardButton.dataset.target).classList.add(CLASSES_EXPANDED)}};_exports.init=selectorId=>{const sidebarMenu=document.querySelector(selectorId+reportSelectors.regions.sidebarMenu),sidebarSearch=sidebarMenu.querySelector(reportSelectors.actions.sidebarSearch),sidebarSearchDebounce=(0,_utils.debounce)(sidebarCardFilter,250);sidebarSearch.addEventListener("keyup",(event=>{const pendingPromise=new _pending.default("core_reportbuilder/sidebar:keyup");sidebarSearchDebounce(event,sidebarMenu),setTimeout((()=>{pendingPromise.resolve()}),250)}))}}));

//# sourceMappingURL=sidebar.min.js.map