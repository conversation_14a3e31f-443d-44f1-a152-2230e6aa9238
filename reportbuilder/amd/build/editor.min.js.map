{"version": 3, "file": "editor.min.js", "sources": ["../src/editor.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder editor\n *\n * @module      core_reportbuilder/editor\n * @copyright   2021 David <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport 'core/inplace_editable';\nimport {addIconToContainer} from 'core/loadingicon';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport Templates from 'core/templates';\nimport {get_string as getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {init as columnsEditorInit} from 'core_reportbuilder/local/editor/columns';\nimport {init as conditionsEditorInit} from 'core_reportbuilder/local/editor/conditions';\nimport {init as filtersEditorInit} from 'core_reportbuilder/local/editor/filters';\nimport {init as sortingEditorInit} from 'core_reportbuilder/local/editor/sorting';\nimport {init as cardviewEditorInit} from 'core_reportbuilder/local/editor/card_view';\nimport {getReport} from 'core_reportbuilder/local/repository/reports';\nimport {createReportModal} from 'core_reportbuilder/local/repository/modals';\n\nlet initialized = false;\n\n/**\n * Initialise editor and all it's modules\n */\nexport const init = () => {\n    columnsEditorInit(initialized);\n    conditionsEditorInit(initialized);\n    filtersEditorInit(initialized);\n    sortingEditorInit(initialized);\n    cardviewEditorInit(initialized);\n\n    // Ensure we only add our listeners once (can be called multiple times by mustache template).\n    if (initialized) {\n        return;\n    }\n\n    // Add event handlers to generic report editor elements.\n    document.addEventListener('click', event => {\n\n        // Toggle between edit and preview mode.\n        const toggleEditViewMode = event.target.closest(reportSelectors.actions.toggleEditPreview);\n        if (toggleEditViewMode) {\n            event.preventDefault();\n\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const pendingPromise = new Pending('core_reportbuilder/reports:get');\n            const toggledEditMode = toggleEditViewMode.dataset.editMode !== \"1\";\n\n            addIconToContainer(toggleEditViewMode)\n                .then(() => getReport(reportElement.dataset.reportId, toggledEditMode))\n                .then(response => Promise.all([\n                    $.parseHTML(response.javascript, null, true).map(node => node.innerHTML).join(\"\\n\"),\n                    Templates.renderForPromise('core_reportbuilder/local/dynamictabs/editor', response),\n                ]))\n                .then(([responseJs, {html, js}]) => Templates.replaceNode(reportElement, html, js + responseJs))\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n\n        // Edit report details modal.\n        const reportEdit = event.target.closest(reportSelectors.actions.reportEdit);\n        if (reportEdit) {\n            event.preventDefault();\n\n            const reportModal = createReportModal(event.target, getString('editreportdetails', 'core_reportbuilder'),\n                reportEdit.dataset.reportId);\n            reportModal.addEventListener(reportModal.events.FORM_SUBMITTED, () => {\n                getString('reportupdated', 'core_reportbuilder')\n                    .then(addToast)\n                    .then(() => {\n                        return window.location.reload();\n                    })\n                    .catch(Notification.exception);\n            });\n            reportModal.show();\n        }\n    });\n\n    initialized = true;\n};\n"], "names": ["initialized", "document", "addEventListener", "event", "toggleEditViewMode", "target", "closest", "reportSelectors", "actions", "toggleEditPreview", "preventDefault", "reportElement", "regions", "report", "pendingPromise", "Pending", "toggledEditMode", "dataset", "editMode", "then", "reportId", "response", "Promise", "all", "$", "parseHTML", "javascript", "map", "node", "innerHTML", "join", "Templates", "renderForPromise", "_ref", "responseJs", "html", "js", "replaceNode", "resolve", "catch", "Notification", "exception", "reportEdit", "reportModal", "events", "FORM_SUBMITTED", "addToast", "window", "location", "reload", "show"], "mappings": "09DA0CIA,aAAc,gBAKE,uBACEA,kCACGA,+BACHA,+BACAA,iCACCA,aAGfA,cAKJC,SAASC,iBAAiB,SAASC,cAGzBC,mBAAqBD,MAAME,OAAOC,QAAQC,gBAAgBC,QAAQC,sBACpEL,mBAAoB,CACpBD,MAAMO,uBAEAC,cAAgBR,MAAME,OAAOC,QAAQC,gBAAgBK,QAAQC,QAC7DC,eAAiB,IAAIC,iBAAQ,kCAC7BC,gBAA0D,MAAxCZ,mBAAmBa,QAAQC,6CAEhCd,oBACde,MAAK,KAAM,sBAAUR,cAAcM,QAAQG,SAAUJ,mBACrDG,MAAKE,UAAYC,QAAQC,IAAI,CAC1BC,gBAAEC,UAAUJ,SAASK,WAAY,MAAM,GAAMC,KAAIC,MAAQA,KAAKC,YAAWC,KAAK,MAC9EC,mBAAUC,iBAAiB,8CAA+CX,cAE7EF,MAAKc,WAAEC,YAAYC,KAACA,KAADC,GAAOA,iBAASL,mBAAUM,YAAY1B,cAAewB,KAAMC,GAAKF,eACnFf,MAAK,IAAML,eAAewB,YAC1BC,MAAMC,sBAAaC,iBAItBC,WAAavC,MAAME,OAAOC,QAAQC,gBAAgBC,QAAQkC,eAC5DA,WAAY,CACZvC,MAAMO,uBAEAiC,aAAc,6BAAkBxC,MAAME,QAAQ,mBAAU,oBAAqB,sBAC/EqC,WAAWzB,QAAQG,UACvBuB,YAAYzC,iBAAiByC,YAAYC,OAAOC,gBAAgB,yBAClD,gBAAiB,sBACtB1B,KAAK2B,YACL3B,MAAK,IACK4B,OAAOC,SAASC,WAE1BV,MAAMC,sBAAaC,cAE5BE,YAAYO,WAIpBlD,aAAc"}