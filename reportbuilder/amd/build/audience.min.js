define("core_reportbuilder/audience",["exports","core/inplace_editable","core/templates","core/notification","core/pending","core/prefetch","core/str","core_form/dynamicform","core/toast","core_reportbuilder/local/repository/audiences","core_reportbuilder/local/selectors","core/fragment","core_form/changechecker"],(function(_exports,_inplace_editable,_templates,_notification,_pending,_prefetch,_str,_dynamicform,_toast,_audiences,reportSelectors,_fragment,_changechecker){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_templates=_interopRequireDefault(_templates),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_dynamicform=_interopRequireDefault(_dynamicform),reportSelectors=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(reportSelectors);let reportId=0,contextId=0;const initAudienceCardForm=audienceCard=>{const audienceFormContainer=audienceCard.querySelector(reportSelectors.regions.audienceFormContainer),audienceForm=new _dynamicform.default(audienceFormContainer,"\\core_reportbuilder\\form\\audience");return audienceForm.addEventListener(audienceForm.events.FORM_SUBMITTED,(data=>{const audienceHeading=audienceCard.querySelector(reportSelectors.regions.audienceHeading),audienceDescription=audienceCard.querySelector(reportSelectors.regions.audienceDescription);return audienceCard.dataset.instanceid=data.detail.instanceid,audienceHeading.innerHTML=data.detail.heading,audienceDescription.innerHTML=data.detail.description,closeAudienceCardForm(audienceCard),(0,_str.get_string)("audiencesaved","core_reportbuilder").then(_toast.add)})),audienceForm.addEventListener(audienceForm.events.FORM_CANCELLED,(()=>{audienceCard.dataset.instanceid>0?closeAudienceCardForm(audienceCard):removeAudienceCard(audienceCard)})),audienceForm},closeAudienceCardForm=audienceCard=>{const audienceFormContainer=audienceCard.querySelector(reportSelectors.regions.audienceFormContainer),NewAudienceFormContainer=audienceFormContainer.cloneNode(!1);audienceCard.querySelector(reportSelectors.regions.audienceForm).replaceChild(NewAudienceFormContainer,audienceFormContainer),audienceCard.querySelector(reportSelectors.regions.audienceDescription).classList.remove("hidden"),audienceCard.querySelector(reportSelectors.actions.audienceEdit).disabled=!1,audienceCard.querySelector(reportSelectors.actions.audienceDelete).disabled=!1},removeAudienceCard=audienceCard=>{audienceCard.remove();const audienceCards=document.querySelector(reportSelectors.regions.audiencesContainer).querySelectorAll(reportSelectors.regions.audienceCard);if(0===audienceCards.length){document.querySelector(reportSelectors.regions.audienceEmptyMessage).classList.remove("hidden")}else{const audienceFirstCardSeparator=audienceCards[0].querySelector(".audience-separator");null==audienceFirstCardSeparator||audienceFirstCardSeparator.remove()}};let initialized=!1;_exports.init=(id,contextid)=>{(0,_prefetch.prefetchStrings)("core_reportbuilder",["audienceadded","audiencedeleted","audiencesaved","deleteaudience","deleteaudienceconfirm"]),(0,_prefetch.prefetchStrings)("core",["delete"]),reportId=id,contextId=contextid,initialized||(document.addEventListener("click",(event=>{const audienceAdd=event.target.closest(reportSelectors.actions.audienceAdd);audienceAdd&&(event.preventDefault(),((className,title)=>{const pendingPromise=new _pending.default("core_reportbuilder/audience:add"),audiencesContainer=document.querySelector(reportSelectors.regions.audiencesContainer),audienceCardLength=audiencesContainer.querySelectorAll(reportSelectors.regions.audienceCard).length,params={classname:className,reportid:reportId,showormessage:audienceCardLength>0,title:title};(0,_fragment.loadFragment)("core_reportbuilder","audience_form",contextId,params).then(((html,js)=>{const audienceCard=_templates.default.appendNodeContents(audiencesContainer,html,js)[0],audienceEmptyMessage=audiencesContainer.querySelector(reportSelectors.regions.audienceEmptyMessage),audienceForm=initAudienceCardForm(audienceCard);return(0,_changechecker.markFormAsDirty)(audienceForm.getFormNode()),audienceEmptyMessage.classList.add("hidden"),(0,_str.get_string)("audienceadded","core_reportbuilder",title)})).then(_toast.add).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)})(audienceAdd.dataset.uniqueIdentifier,audienceAdd.dataset.name));const audienceEdit=event.target.closest(reportSelectors.actions.audienceEdit);if(audienceEdit){const audienceEditCard=audienceEdit.closest(reportSelectors.regions.audienceCard);event.preventDefault(),(audienceCard=>{const pendingPromise=new _pending.default("core_reportbuilder/audience:edit");initAudienceCardForm(audienceCard).load({id:audienceCard.dataset.instanceid}).then((()=>{const audienceFormContainer=audienceCard.querySelector(reportSelectors.regions.audienceFormContainer),audienceDescription=audienceCard.querySelector(reportSelectors.regions.audienceDescription),audienceEdit=audienceCard.querySelector(reportSelectors.actions.audienceEdit);return audienceFormContainer.classList.remove("hidden"),audienceDescription.classList.add("hidden"),audienceEdit.disabled=!0,pendingPromise.resolve()})).catch(_notification.default.exception)})(audienceEditCard)}const audienceDelete=event.target.closest(reportSelectors.actions.audienceDelete);audienceDelete&&(event.preventDefault(),(audienceDelete=>{const audienceCard=audienceDelete.closest(reportSelectors.regions.audienceCard),audienceTitle=audienceCard.dataset.title;_notification.default.saveCancelPromise((0,_str.get_string)("deleteaudience","core_reportbuilder",audienceTitle),(0,_str.get_string)("deleteaudienceconfirm","core_reportbuilder",audienceTitle),(0,_str.get_string)("delete","core"),{triggerElement:audienceDelete}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/audience:delete");return(0,_audiences.deleteAudience)(reportId,audienceCard.dataset.instanceid).then((()=>(0,_toast.add)((0,_str.get_string)("audiencedeleted","core_reportbuilder",audienceTitle)))).then((()=>(removeAudienceCard(audienceCard),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))})(audienceDelete))})),initialized=!0)}}));

//# sourceMappingURL=audience.min.js.map