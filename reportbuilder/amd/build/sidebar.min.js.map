{"version": 3, "file": "sidebar.min.js", "sources": ["../src/sidebar.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder sidebar component\n *\n * @module      core_reportbuilder/sidebar\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Pending from 'core/pending';\nimport {debounce} from 'core/utils';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\n\nconst DEBOUNCE_TIMER = 250;\n\nconst CLASSES = {\n    EXPANDED: 'show',\n    COLLAPSED: 'collapsed',\n    HIDE: 'd-none',\n};\n\n/**\n * Initialise module\n *\n * @param {Event} event\n * @param {Element} sidebarMenu\n */\nconst sidebarCardFilter = (event, sidebarMenu) => {\n    const pendingPromise = new Pending('core_reportbuilder/sidebar:cardFilter');\n\n    const sidebarCards = sidebarMenu.querySelectorAll(reportSelectors.regions.sidebarCard);\n    const sidebarItems = sidebarMenu.querySelectorAll(reportSelectors.regions.sidebarItem);\n    const searchTerm = event.target.value.toLowerCase();\n\n    // Toggle items according to match against search term.\n    sidebarItems.forEach(item => {\n        const itemContent = item.textContent.toLowerCase();\n        item.classList.toggle(CLASSES.HIDE, !itemContent.includes(searchTerm));\n    });\n\n    // Toggle cards according to whether they have any visible items.\n    sidebarCards.forEach(card => {\n        const visibleItems = card.querySelectorAll(`${reportSelectors.regions.sidebarItem}:not(.${CLASSES.HIDE})`);\n        card.classList.toggle(CLASSES.HIDE, !visibleItems.length);\n\n        expandCard(card);\n    });\n\n    pendingPromise.resolve();\n};\n\n/**\n * Show a collapsed card.\n * This function simulates the behaviour of JQuery show method on a collapsible element.\n *\n * @param {Element} card\n */\nconst expandCard = (card) => {\n    let cardButton = card.querySelector('[data-toggle=\"collapse\"]');\n    if (cardButton.classList.contains(CLASSES.COLLAPSED)) {\n        cardButton.classList.remove(CLASSES.COLLAPSED);\n        cardButton.setAttribute('aria-expanded', \"true\");\n        let cardContent = card.querySelector(cardButton.dataset.target);\n        cardContent.classList.add(CLASSES.EXPANDED);\n    }\n};\n\n/**\n * Initialise module\n *\n * @param {string} selectorId\n */\nexport const init = (selectorId) => {\n    const sidebarMenu = document.querySelector(selectorId + reportSelectors.regions.sidebarMenu);\n    const sidebarSearch = sidebarMenu.querySelector(reportSelectors.actions.sidebarSearch);\n\n    // Debounce the event listener to allow the user to finish typing.\n    const sidebarSearchDebounce = debounce(sidebarCardFilter, DEBOUNCE_TIMER);\n    sidebarSearch.addEventListener('keyup', event => {\n        const pendingPromise = new Pending('core_reportbuilder/sidebar:keyup');\n\n        sidebarSearchDebounce(event, sidebarMenu);\n        setTimeout(() => {\n            pendingPromise.resolve();\n        }, DEBOUNCE_TIMER);\n    });\n};\n"], "names": ["CLASSES", "sidebarCardFilter", "event", "sidebarMenu", "pendingPromise", "Pending", "sidebarCards", "querySelectorAll", "reportSelectors", "regions", "sidebarCard", "sidebarItems", "sidebarItem", "searchTerm", "target", "value", "toLowerCase", "for<PERSON>ach", "item", "itemContent", "textContent", "classList", "toggle", "includes", "card", "visibleItems", "length", "expandCard", "resolve", "cardButton", "querySelector", "contains", "remove", "setAttribute", "dataset", "add", "selectorId", "document", "sidebarSearch", "actions", "sidebarSearchDebounce", "addEventListener", "setTimeout"], "mappings": ";;;;;;;mlCA6BMA,iBACQ,OADRA,kBAES,YAFTA,aAGI,SASJC,kBAAoB,CAACC,MAAOC,qBACxBC,eAAiB,IAAIC,iBAAQ,yCAE7BC,aAAeH,YAAYI,iBAAiBC,gBAAgBC,QAAQC,aACpEC,aAAeR,YAAYI,iBAAiBC,gBAAgBC,QAAQG,aACpEC,WAAaX,MAAMY,OAAOC,MAAMC,cAGtCL,aAAaM,SAAQC,aACXC,YAAcD,KAAKE,YAAYJ,cACrCE,KAAKG,UAAUC,OAAOtB,cAAemB,YAAYI,SAASV,gBAI9DP,aAAaW,SAAQO,aACXC,aAAeD,KAAKjB,2BAAoBC,gBAAgBC,QAAQG,6BAAoBZ,mBAC1FwB,KAAKH,UAAUC,OAAOtB,cAAeyB,aAAaC,QAElDC,WAAWH,SAGfpB,eAAewB,WASbD,WAAcH,WACZK,WAAaL,KAAKM,cAAc,+BAChCD,WAAWR,UAAUU,SAAS/B,mBAAoB,CAClD6B,WAAWR,UAAUW,OAAOhC,mBAC5B6B,WAAWI,aAAa,gBAAiB,QACvBT,KAAKM,cAAcD,WAAWK,QAAQpB,QAC5CO,UAAUc,IAAInC,kCASboC,mBACXjC,YAAckC,SAASP,cAAcM,WAAa5B,gBAAgBC,QAAQN,aAC1EmC,cAAgBnC,YAAY2B,cAActB,gBAAgB+B,QAAQD,eAGlEE,uBAAwB,mBAASvC,kBAhEpB,KAiEnBqC,cAAcG,iBAAiB,SAASvC,cAC9BE,eAAiB,IAAIC,iBAAQ,oCAEnCmC,sBAAsBtC,MAAOC,aAC7BuC,YAAW,KACPtC,eAAewB,YAtEJ"}