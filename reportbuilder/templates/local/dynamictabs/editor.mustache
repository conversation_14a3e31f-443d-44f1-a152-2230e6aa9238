{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/local/dynamictabs/editor

    Template for the custom report editor

    Example context (json):
    {
        "id": 1,
        "type": 1,
        "table": "table",
        "editmode": true,
        "sidebarmenucards": [{
            "menucards": [{
                "name": "General",
                "items": [{
                    "name": "Manually added users",
                    "identifier": "core_reportbuilder:users"
                }]
            }]
        }],
        "conditions": [{
            "hasavailableconditions": true,
            "availableconditions": [{
                "optiongroup": [{
                    "text": "User",
                    "values": [{
                        "value": 1,
                        "visiblename": "User name"
                    }]
                }]
            }],
            "hasactiveconditions": true,
            "activeconditionsform": "form"
        }],
        "filters": [{
            "hasavailablefilters": true,
            "availablefilters": [{
                "optiongroup": [{
                    "text": "User",
                    "values": [{
                        "value": 1,
                        "visiblename": "User name"
                    }]
                }]
            }],
            "hasactivefilters": true,
            "activefilters": [{
                "heading": "Email address",
                "entityname": "user",
                "headingeditable": "Email address"
            }]
        }],
        "sorting": [{
            "hassortablecolumns": true,
            "sortablecolumns": [{
                "id": 1,
                "title": "Email address",
                "sortdirection": "4",
                "sortenabled": true,
                "sortorder": 1,
                "sorticon": [{
                    "key": "t/uplong",
                    "component": "core",
                    "title": "Sort column 'Email address' ascending"
                }],
                "heading": "Email address"
            }]
        }],
        "cardview": {
            "form": "form"
        }
    }
}}
<h2 class="sr-only">{{#str}} editor, core_reportbuilder {{/str}}</h2>
<div class="reportbuilder-report"
     data-region="core_reportbuilder/report"
     data-report-id="{{id}}"
     data-report-type="{{type}}"
     data-parameter="[]"
     {{#editmode}}data-editing{{/editmode}}>
    <div class="reportbuilder-wrapper d-flex flex-column flex-lg-row">
        {{#editmode}}
            {{! Menu sidebar }}
            {{> core_reportbuilder/local/sidebar-menu/area}}
        {{/editmode}}
        {{! Report }}
        <div class="reportbuilder-report-container">
            <div class="{{#editmode}}p-2 border{{/editmode}}">
                <div data-region="core_reportbuilder/report-header" class="dropdown d-flex justify-content-end">
                    {{! Preview/Edit button }}
                    <button data-action="toggle-edit-preview" class="btn btn-outline-secondary"
                        {{#editmode}}
                            data-edit-mode="1" title="{{#str}} switchpreview, core_reportbuilder {{/str}}"
                        {{/editmode}}
                        {{^editmode}}
                            data-edit-mode="0" title="{{#str}} switchedit, core_reportbuilder {{/str}}"
                        {{/editmode}}
                    >
                        {{#editmode}}
                            {{#pix}} i/preview, core {{/pix}}
                            {{#str}} preview, core {{/str}}
                        {{/editmode}}
                        {{^editmode}}
                            {{#pix}} t/editstring, core {{/pix}}
                            {{#str}} edit, core {{/str}}
                        {{/editmode}}
                    </button>
                    {{^editmode}}
                        {{! Filters }}
                        {{#filterspresent}}
                            {{>core_reportbuilder/local/filters/area}}
                        {{/filterspresent}}
                    {{/editmode}}
                </div>
                <div class="reportbuilder-editor-table-container mt-2">
                    {{! Table }}
                    {{{table}}}
                </div>
            </div>
        </div>
        {{#editmode}}
            {{! Settings sidebar }}
            {{> core_reportbuilder/local/settings/area}}
        {{/editmode}}
    </div>
</div>

{{#js}}
    require(['core_reportbuilder/editor', 'core_reportbuilder/report'],
    function(editor, report) {
        editor.init();
        report.init();
    });
{{/js}}
