{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/local/dynamictabs/schedules

    Template for Schedules tab

    Example context (json):
    {
        "report": "The report content",
        "reportid": 10
    }
}}
<div class="reportbuilder-schedules-container">
    <div class="d-flex mb-2">
        <button class="btn btn-primary ml-auto" data-action="schedule-create">{{#str}} newschedule, core_reportbuilder {{/str}}</button>
    </div>
    {{{ report }}}
</div>

{{#js}}
    require(['core_reportbuilder/schedules'], function(schedules) {
        schedules.init({{reportid}});
    });
{{/js}}
