{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/local/settings/area

    Template for system report filters area

    Example context (json):
    {
        "conditions": [{
            "hasavailableconditions": true,
            "availableconditions": [{
                "optiongroup": [{
                    "text": "User",
                    "values": [{
                        "value": 1,
                        "visiblename": "User name"
                    }]
                }]
            }],
            "hasactiveconditions": true,
            "activeconditionsform": "form"
        }],
        "filters": [{
            "hasavailablefilters": true,
            "availablefilters": [{
                "optiongroup": [{
                    "text": "User",
                    "values": [{
                        "value": 1,
                        "visiblename": "User name"
                    }]
                }]
            }],
            "hasactivefilters": true,
            "activefilters": [{
                "heading": "Email address",
                "entityname": "user",
                "headingeditable": "Email address"
            }]
        }],
        "sorting": [{
            "hassortablecolumns": true,
            "sortablecolumns": [{
                "id": 1,
                "title": "Email address",
                "sortdirection": "4",
                "sortenabled": true,
                "sortorder": 1,
                "sorticon": [{
                    "key": "t/uplong",
                    "component": "core",
                    "title": "Sort column 'Email address' ascending"
                }],
                "heading": "Email address"
            }]
        }],
        "cardview": {
            "form": "form"
        }
    }
}}

<div class="reportbuilder-sidebar-settings d-flex flex-column mt-3 mt-lg-0 mb-3 mb-lg-0 ml-lg-3">

            {{< core_reportbuilder/toggle_card }}
                {{$collapsed}}collapsed{{/collapsed}}
                {{$id}}settingsconditions{{/id}}
                {{$header}}{{#str}} conditions, core_reportbuilder {{/str}}{{/header}}
                {{$helpicon}}{{{conditions.helpicon}}}{{/helpicon}}
                {{$body}}
                    {{> core_reportbuilder/local/settings/conditions}}
                {{/body}}
            {{/ core_reportbuilder/toggle_card }}

            {{< core_reportbuilder/toggle_card }}
                {{$collapsed}}collapsed{{/collapsed}}
                {{$id}}settingsfilters{{/id}}
                {{$header}}{{#str}} filters, core_reportbuilder {{/str}}{{/header}}
                {{$helpicon}}{{{filters.helpicon}}}{{/helpicon}}
                {{$body}}
                    {{> core_reportbuilder/local/settings/filters}}
                {{/body}}
            {{/ core_reportbuilder/toggle_card }}

            {{< core_reportbuilder/toggle_card }}
                {{$collapsed}}collapsed{{/collapsed}}
                {{$id}}settingssorting{{/id}}
                {{$header}}{{#str}} sorting, core_reportbuilder {{/str}}{{/header}}
                {{$helpicon}}{{{sorting.helpicon}}}{{/helpicon}}
                {{$body}}
                    {{> core_reportbuilder/local/settings/sorting}}
                {{/body}}
            {{/ core_reportbuilder/toggle_card }}

            {{< core_reportbuilder/toggle_card }}
                {{$collapsed}}collapsed{{/collapsed}}
                {{$id}}settingscardview{{/id}}
                {{$header}}{{#str}} cardview, core_reportbuilder {{/str}}{{/header}}
                {{$helpicon}}{{{cardview.helpicon}}}{{/helpicon}}
                {{$body}}
                    {{> core_reportbuilder/local/settings/card_view}}
                {{/body}}
            {{/ core_reportbuilder/toggle_card }}

</div>
