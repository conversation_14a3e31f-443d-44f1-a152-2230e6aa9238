{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/local/report/debug

    Template for custom report debug

    Example context (json):
    {
        "query": "SELECT foo FROM {bar} WHERE baz = :rbparam1",
        "params": [
            {
                "param": "rbparam1",
                "value": 42
            }
        ],
        "duration": "0.124 secs"
    }
}}
<details>
    <summary>{{#str}} debuginfo, core_debug {{/str}}</summary>
    <code class="d-block mb-2">{{query}}</code>
    {{#params}}
        <code class="d-block mb-2">{{param}} => {{value}}</code>
    {{/params}}
    {{#duration}}<code class="d-block">{{.}}</code>{{/duration}}
</details>
