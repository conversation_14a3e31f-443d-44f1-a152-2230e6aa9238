#!/bin/bash

#echo "Starting development environment update..."
#
## Step 1: Initialize and fetch submodules
#git submodule update --init
#
## Step 2: Set each submodule to track the development branch
#git submodule foreach '
#    echo "=== Processing $(pwd) ==="
#    git fetch origin
#
#    # Check if development branch exists locally; if not, create tracking branch
#    if ! git show-ref --verify --quiet refs/heads/development; then
#        echo "Creating local development branch tracking origin/development"
#        git checkout -b development origin/development
#    else
#        echo "Checking out existing development branch"
#        git checkout development
#    fi
#
#    git pull origin development
#    echo "Successfully updated to latest development branch"
#    echo "----------------------------------------"
#'
#
#echo "Development environment update completed!"

# Source the validation script
source "$(dirname "$0")/validate_environment.sh"

# Validate environment
if ! validate_environment; then
    exit 1
fi

echo "Starting submodule update for $ENVIRONMENT environment using branch: $GIT_BRANCH"

# Step 1: Initialize and fetch submodules
git submodule update --init

# Step 2: Set each submodule to track the specified branch
git submodule foreach '
    echo "=== Processing $(pwd) ==="
    git fetch origin

    # Check if specified branch exists
    if git show-ref --verify --quiet "refs/heads/'$GIT_BRANCH'"; then
        echo "Checking out existing '"$GIT_BRANCH"' branch"
        git checkout '$GIT_BRANCH'
        git pull origin '$GIT_BRANCH'
    elif git ls-remote --heads origin '$GIT_BRANCH' | grep -q '$GIT_BRANCH'; then
        echo "Creating local '"$GIT_BRANCH"' branch from remote"
        git checkout -b '$GIT_BRANCH' origin/'$GIT_BRANCH'
    else
        echo "Error: '"$GIT_BRANCH"' branch does not exist in $(pwd)"
        exit 1
    fi

    # Verify we are on the correct branch
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    if [ "$CURRENT_BRANCH" != "'$GIT_BRANCH'" ]; then
        echo "Error: Failed to checkout '"$GIT_BRANCH"' branch in $(pwd)"
        exit 1
    fi
'

# Check if the submodule update was successful
if [ $? -eq 0 ]; then
    echo "Successfully updated all submodules to $GIT_BRANCH branch"
else
    echo "Error: Failed to update some submodules"
    exit 1
fi

