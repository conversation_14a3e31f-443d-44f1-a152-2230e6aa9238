#!/bin/bash

# Define the base directory (where the script is run from)
BASE_DIR=$(pwd)

# Define the destination directory for language files
DEST_DIR="/opt/homebrew/var/language_files_collection2"

# Create the destination directory if it doesn't exist
if [ ! -d "$DEST_DIR" ]; then
    mkdir -p "$DEST_DIR"
    echo "Created directory: $DEST_DIR"
fi

# Read the .gitmodules file and process each submodule
while IFS= read -r line; do
    if [[ $line =~ path[[:space:]]*=[[:space:]]*(.+)$ ]]; then
        # Extract the submodule path
        SUBMODULE_PATH="${BASH_REMATCH[1]}"

        # Check if lang/en directory exists in the submodule
        if [ -d "$BASE_DIR/$SUBMODULE_PATH/lang/en" ]; then
            echo "Processing language files from: $SUBMODULE_PATH"

            # Copy all PHP files from the lang/en directory directly
            for file in "$BASE_DIR/$SUBMODULE_PATH/lang/en/"*.php; do
                if [ -f "$file" ]; then
                    cp "$file" "$DEST_DIR/"
                fi
            done

            # If copy was successful, print confirmation
            if [ $? -eq 0 ]; then
                echo "✓ Language files copied from $SUBMODULE_PATH"
            else
                echo "! No PHP files found in $SUBMODULE_PATH/lang/en"
            fi
        else
            echo "× No lang/en directory found in $SUBMODULE_PATH"
        fi
    fi
done < "$BASE_DIR/.gitmodules"

echo -e "\nLanguage files collection completed!"
echo "Files are stored in: $DEST_DIR"

# List all collected files
echo -e "\nCollected language files:"
find "$DEST_DIR" -type f -name "*.php" | while read -r file; do
    echo "- $(basename $file)"
done
