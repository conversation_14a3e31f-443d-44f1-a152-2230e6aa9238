#!/bin/bash

# Define target languages
#target_languages=("fr")
#
## Function to process a single file
#translate_file() {
#    local file="$1"
#    local lang="$2"
#    echo "Translating $(basename "$file") to $lang..."
#
#    # Run PHP command and wait for it to complete
#    php /opt/homebrew/var/www/translate/index.php --source_filepath="$file" --source_lang=en --target_lang="$lang"
#
#    # Check if the command was successful
#    if [ $? -eq 0 ]; then
#        echo "Successfully translated $(basename "$file") to $lang"
#    else
#        echo "Error translating $(basename "$file") to $lang"
#    fi
#
#    # Add a small delay to ensure process completion
#    sleep 1
#}
#
## Main loop
#for file in /opt/homebrew/var/www/enphunew/language_files_collection/*.php; do
#    for lang in "${target_languages[@]}"; do
#        # Process each file synchronously
#        translate_file "$file" "$lang"
#
#        # Wait for any background processes to finish
#        wait
#    done
#done
#
#echo "All translations completed!"


# Define target languages (example: Greek, Spanish, French)
#target_languages=("fr")
#
#for file in /opt/homebrew/var/www/enphunew/language_files_collection/*.php; do
#    for lang in "${target_languages[@]}"; do
#        php /opt/homebrew/var/www/translate/index.php --source_filepath="$file" --source_lang=en --target_lang="$lang"
#    done
#done

for file in /opt/homebrew/var/www/enphunew/language_files_collection/*.php; do
    echo "php /opt/homebrew/var/www/translate/index.php --source_filepath=\"$file\" --source_lang=en --target_lang=fr"
done

