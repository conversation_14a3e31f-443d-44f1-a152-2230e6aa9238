<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
global $CFG;
global $DB;

@error_reporting(E_ALL | E_STRICT); // NOT FOR PRODUCTION SERVERS!
@ini_set('display_errors', '1'); // NOT FOR PRODUCTION SERVERS!
$CFG->debug = (E_ALL | E_STRICT); // === DEBUG_DEVELOPER - NOT FOR PRODUCTION SERVERS!
$CFG->debugdisplay = 1;

$context_system = context_system::instance();
cron_setup_user();
$mainadminuser = get_admin();

$transaction = $DB->start_delegated_transaction();
try {

  $sql = "INSERT INTO {tool_standalonecourses} (
                          id, 
                          courseid,
                          isactive,
                          isarchived,
                          sortorder,
                          usermodified,
                          timecreated,
                          timemodified
                          )
          SELECT 
             id, 
             courseid, 
             CASE WHEN deleted = 0 THEN 1 ELSE 0 END AS isactive, 
             0 AS isarchived, 
             sortorder, 
             ".$mainadminuser->id." AS usermodified, 
             UNIX_TIMESTAMP() AS timecreated,  
             UNIX_TIMESTAMP() AS timemodified 
          FROM {standalone_courses}";

  $DB->execute($sql);
  migrate_langs();
  migrate_slangs();
  migrate_availability();
  migrate_requests();

  $transaction->allow_commit();

}catch (Exception $e) {
    mtrace('Standalone courses migration failed');
    $transaction->rollback($e);
}
mtrace('Standalone courses migrated successfully');

function migrate_langs() {
  global $DB;
  $standalone_courses = $DB->get_records('standalone_courses');
  foreach ($standalone_courses as $standalone_course) {
    $courselangs = $standalone_course->audio_languages;
    if ($courselangs){
      $courselangsarr = explode(',', $courselangs);
      foreach ($courselangsarr as $lang){
        $record = new stdClass();
        $record->standalonecourseid = $standalone_course->id;
        $record->lang = $lang;
        $DB->insert_record('tool_standalonecourses_lang', $record);
      }
    }
  }
}

function migrate_slangs() {
  global $DB;
  $standalone_courses = $DB->get_records('standalone_courses');
  foreach ($standalone_courses as $standalone_course) {
    $coursesubtitlelangs = $standalone_course->subtitle_languages;
    if ($coursesubtitlelangs){
      $coursesubtitlelangsarr = explode(',', $coursesubtitlelangs);
      foreach ($coursesubtitlelangsarr as $slang){
        $record = new stdClass();
        $record->standalonecourseid = $standalone_course->id;
        $record->lang = $slang;
        $DB->insert_record('tool_standalonecourses_slang', $record);
      }
    }
  }
}

function migrate_availability() {
  global $DB;
  $standalone_courses = $DB->get_records('tool_standalonecourses');
  $departmentrecords = $DB->get_records_menu('local_orgs_departments', ['is_country' => 1], '', 'idnumber, id');
  foreach ($standalone_courses as $standalone_course) {
    // get the course countries
    $coursecountries = $DB->get_records('course_to_country', ['courseid' => $standalone_course->courseid]);
    foreach ($coursecountries as $coursecountry) {
      if (!$departmentrecords[$coursecountry->country_code]){
        continue;
      }
      $record = new stdClass();
      $record->standalonecourseid = $standalone_course->id;
      $record->departmentid = $departmentrecords[$coursecountry->country_code];
      $DB->insert_record('tool_standalonecourses_deps', $record);
    }
  }
}

function migrate_requests() {
  global $DB;
  $sql = "INSERT INTO {tool_sc_requests} (
                          courseid, 
                          userid, 
                          approval_status, 
                          timecreated, 
                          timemodified,
                          is_first_request,
                          approved_by,
                          is_request_preapproved
                          )
        SELECT 
          courseid, 
          userid, 
          approval_status, 
          timecreated, 
          timemodified, 
          is_first_request,
          approved_by, 
          is_request_preapproved
        FROM {standalone_course_requests}";

  $DB->execute($sql);
}