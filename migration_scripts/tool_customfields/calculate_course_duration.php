<?php
/*
 * This script should calculate course completion estimated time based on activities duration of the course.
 *
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';

core_php_time_limit::raise();

raise_memory_limit(MEMORY_EXTRA);

// Emulate normal session - we use admin account by default.
cron_setup_user();

global $DB;

$courses = get_courses();

$customfieldshortname = 'local_catalog_course_duration';

// Get the field ID from the custom field's shortname.
$field = $DB->get_record('customfield_field', ['shortname' => $customfieldshortname]);

if (!$field) {
  die('Course duration custom field not found.');
}

$fieldid = $field->id;

foreach ($courses as $course) {
  $couduration = $seconds = 0;
  $sections = $DB->get_records('course_sections', array('course' => $course->id));
  $coursesections = count($sections);
  try{
    foreach ($sections as $section) {
      if ($section->duration != 0) {
        $time = substr($section->duration, -5);
        if ($time == 'hours' || $time == 'Hours') {
          $timestr = substr($section->duration, 0, 7);
          $dura = explode(':', $timestr);
          $seconds = ($dura[0] * 60 * 60) + ($dura[1] * 60) + $dura[2];
        } else {
          $timestr = substr(ltrim($section->duration), 0, 5);
          $dura = explode(':', $timestr);
          $seconds = ($dura[0] * 60) + $dura[1];
        }
      }
      $couduration += $seconds;
    }
  }catch (Exception $e){
    continue;
  }

  // Now we need to set this to course custom field value
  $data = new stdClass();
  $data->fieldid = $fieldid;
  $data->instanceid = $course->id;
  $data->intvalue = $couduration;
  $data->value = $couduration;
  $data->valueformat = 0;
  $data->timecreated = time();
  $data->timemodified = time();
  $data->contextid = (context_course::instance($course->id))->id;
  if (!$DB->record_exists('customfield_data',
      ['fieldid' => $fieldid, 'instanceid' => $course->id])){
    $DB->insert_record('customfield_data', $data);
    mtrace("Duration updated for course $course->id");
  }
}




