<?php

/*
 * This script should fetch courses detail from learning_plan_courses and insert into lp_sets and lp_set_items.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

require_once __DIR__ . '/../blocks/learning_plan/lib.php';

global $DB;

use block_learning_plan\helper\lp_sets_helper;
use block_learning_plan\helper\lp_set_items_helper;

$sql = "SELECT * FROM {learning_plan_courses} lpc 
         JOIN {learning_learningplan} lp ON lp.id=lpc.learningplanid
         JOIN {course} c ON lpc.course=c.id
         ORDER BY lpc.learningplanid,lpc.learningplanstage,lpc.courseorder ASC";

try{
  $transaction = $DB->start_delegated_transaction();
  $recordset = $DB->get_recordset_sql($sql);
  $prev_lpid = 0;
  $prev_lpstage = 0;
  foreach($recordset as $record) {
    $lpid = $record->learningplanid;
    $lpstage = $record->learningplanstage;
    $courseid = $record->course;
//    $nextoperator = $record->nextcourseoperator;
    //All the courses and Levels are sperated by then operator. So I am writing script considering this situation.
    if($prev_lpid!=$lpid || $prev_lpstage!=$lpstage) {

      $setdata = new stdClass();
      $setdata->lpid = $lpid;
      $setdata->label = 'LEVEL '.$lpstage;
      $setdata->completiontype = COMPLETIONTYPE_ALL;

      $filter = (array)$setdata;
//      echo "Trying to save lp_sets";
//      print_object($setdata);

      $lp_set = lp_sets_helper::save($setdata, $filter);
    }
    $itemdata = new stdClass();
    $itemdata->course_id = $courseid;
    $itemdata->lpset_id = $lp_set['id'];

    $filter = (array)$itemdata;

//    echo "Trying to save lp_set_items";
//    print_object($itemdata);

    $item = lp_set_items_helper::save($itemdata, $filter);

    //Set current lpid and lpstage as previous lpid and lpstage
    $prev_lpid = $lpid;
    $prev_lpstage = $lpstage;
  }
  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
