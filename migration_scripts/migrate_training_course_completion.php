<?php

/*
 * Note: This script should run after migrate_training_courses.php
 *
 * This script should create set_item_completion data based user's lp enrolment and course completions.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

use block_learning_plan\helper\lp_set_item_completion_helper;


$sql = "SELECT lpr.userid,lpr.lpid,lpc.course,lpsi.id as itemid,cc.timecompleted FROM {learning_plan_requests} lpr 
        JOIN {learning_learningplan} lp ON lpr.lpid = lp.id 
        JOIN {learning_plan_courses} lpc ON lpc.learningplanid = lp.id
        JOIN {course_completions} cc ON cc.course = lpc.course AND cc.userid = lpr.userid AND cc.timecompleted IS NOT NULL
        JOIN {lp_sets} lps ON lps.lpid = lpr.lpid
        JOIN {lp_set_items} lpsi ON lpsi.lpset_id=lps.id AND lpsi.course_id=lpc.course
        ORDER BY cc.timecompleted ASC";
try{
  $transaction = $DB->start_delegated_transaction();
  $recordset = $DB->get_recordset_sql($sql);

  $insert_sql = "INSERT INTO `mdl_lp_set_item_completion` 
  (`item_id`, `userid`, `timecompleted`, `is_cron_processed`,`cron_process_time`,`usermodified`,`timecreated`,`timemodified`) VALUES ";

  $values_sql = [];
  $counter = 0;

  foreach($recordset as $record) {
//    $data = new stdClass();
//    $data->item_id = $record->itemid;
//    $data->userid = $record->userid;

//    $filter = (array)$data;

//    $data->timecompleted = $record->timecompleted;
//    $data->is_cron_processed = 1;
//    $data->cron_process_time = $record->timecompleted;

//    lp_set_item_completion_helper::save($data, $filter);

    $values_sql[] = "({$record->itemid}, {$record->userid}, {$record->timecompleted}, 1, {$record->timecompleted}, 2, {$record->timecompleted}, {$record->timecompleted})";


    if ($counter > 1000) {
      $values_sql_str = implode(',', $values_sql);
      $final_sql = $insert_sql . $values_sql_str;
      $DB->execute($final_sql);
      $counter = 0;
      $values_sql = [];
      $values_sql_str = '';
      $final_sql = '';
    }

    $counter++;

  }
  $values_sql_str = implode(',', $values_sql);
  $final_sql = $insert_sql . $values_sql_str;

//  print_object($final_sql);

  $DB->execute($final_sql);
  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
