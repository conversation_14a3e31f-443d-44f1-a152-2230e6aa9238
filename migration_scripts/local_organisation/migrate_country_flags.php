<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

use local_organisation\country_branding\flag;
require_once __DIR__ . '/../../config.php';

global $DB;

$sql = "SELECT lmcf.id,
               lmcf.country_code,
               lmcf.flag_pathnamehash,
               lgr.name,
               lgr.id AS regionid 
        FROM {local_multitenancyui_c_flags} lmcf
		JOIN {local_geography_country} lgc ON lgc.iso2 = lmcf.country_code
		JOIN {local_geography_region} lgr ON lgr.id = lgc.region
		GROUP BY regionid,lmcf.country_code
		ORDER BY lgr.sortorder, lgc.sortorder ASC";

$old_country_records = $DB->get_records_sql($sql, []);

$context_system = context_system::instance();
$fs = get_file_storage();
$filemanageroptions = flag::get_flag_file_options();

foreach ($old_country_records as $old_country_record) {
  // get the country code
  $country_code = $old_country_record->country_code;
  $country_id = $old_country_record->id;

  $newcountry = $DB->get_record('local_orgs_departments', ['is_country' => '1', 'idnumber' => $country_code]);
  if (!$newcountry){
    continue;
  }

  $flagfile = $fs->get_file_by_hash($old_country_record->flag_pathnamehash);

  //$flagfiles = $fs->get_area_files($context_system->id, 'local_multitenancyui', 'countryflags',  $country_id, 'filesize DESC', false);
  //$flagfile = reset($flagfiles);
  if ($flagfile){
    // We need to put this file in as new country table flag
    $filerecord = [
        'component' => 'local_organisation',
        'filearea' => 'flags',
        'itemid' => $newcountry->id,
        'contextid' => $context_system->id,
        'filepath' => '/',
        'filename' => $flagfile->get_filename()
    ];
    // before creating we need to delete any existing flag
    $fs->delete_area_files($context_system->id, 'local_organisation', 'flags', $newcountry->id);
    $fs->create_file_from_storedfile($filerecord, $flagfile);
  }
}

mtrace('Flags copied successfully');