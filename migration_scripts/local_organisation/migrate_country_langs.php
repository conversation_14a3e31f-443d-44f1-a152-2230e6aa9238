<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';

global $DB;
//$old_country_records = $DB->get_records('local_geography_country', null, 'countrycode');

$sql = "SELECT lgl.id,
               lgl.name,
               lgl.code,
               lgc.iso2, 
               lgc.id AS countryid
        FROM {local_geography_language} lgl
		JOIN {local_geography_country} lgc ON lgc.id = lgl.country
        WHERE lgl.deleted = 0";

$old_country_records = $DB->get_records_sql($sql, []);

$context_system = context_system::instance();
$mainadminuser = get_admin();

$transaction = $DB->start_delegated_transaction();

try {
  foreach ($old_country_records as $old_country_record) {
    // get the country code
    $country_code = $old_country_record->iso2;
    $country_id = $old_country_record->countryid;

    $newcountry = $DB->get_record('local_orgs_departments', ['is_country' => '1', 'idnumber' => $country_code]);
    if (!$newcountry){
      continue;
    }

    $lang = new stdClass();
    $lang->departmentid = $newcountry->id;
    $lang->lang = $old_country_record->code;
    $lang->usermodified = $mainadminuser->id;
    $lang->timecreated = time();
    $lang->timemodified = time();

    $DB->insert_record('local_orgs_country_language', $lang);

  }
  $transaction->allow_commit();
}catch (Exception $e) {
    $transaction->rollback($e);
}

mtrace('Langs copied successfully');
