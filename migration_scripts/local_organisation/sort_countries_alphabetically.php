<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

// Adjust the path according to where you place this script
require_once __DIR__ . '/../../config.php'; // Assuming placed in migration_scripts/local_organisation
require_once($CFG->libdir . '/clilib.php');
// Ensure the hierarchy class is available (<PERSON><PERSON><PERSON>'s autoloader should handle this, but explicit require can be safer for CLI)
require_once($CFG->dirroot . '/local/organisation/classes/hierarchy/department.php');

global $DB;

// Define constants if they are not already defined (though they should be by Moodle's environment)
if (!defined('HIERARCHY_ITEM_ABOVE')) {
  define('HIERARCHY_ITEM_ABOVE', 1);
}
if (!defined('HIERARCHY_ITEM_BELOW')) {
  define('HIERARCHY_ITEM_BELOW', -1); // Assuming 0 based on sort.php, adjust if different
}


mtrace('Starting alphabetical sorting of country departments within each parent using hierarchy functions...');

$transaction = $DB->start_delegated_transaction();
try {
  // Load the department hierarchy helper class
  $department_hierarchy = \local_organisation\hierarchy\department::load_hierarchy();

  // 1. Fetch all departments to group them by parent initially
  // Order by parentid first, then sortthread to process siblings together
  $sql = "SELECT id, fullname, parentid, is_country, sortthread
              FROM {local_orgs_departments}
          ORDER BY parentid, sortthread";
  $all_departments = $DB->get_records_sql($sql);

  if (empty($all_departments)) {
    mtrace('No departments found in the local_orgs_departments table.');
    $transaction->allow_commit(); // Commit empty transaction
    exit(0);
  }

  // 2. Group departments by parent ID
  $children_by_parent = [];
  foreach ($all_departments as $dept) {
    if (!isset($children_by_parent[$dept->parentid])) {
      $children_by_parent[$dept->parentid] = [];
    }
    // Store the original object, keyed by ID for easy lookup
    $children_by_parent[$dept->parentid][$dept->id] = $dept;
  }
  unset($all_departments); // Free memory

  // 3. Process each parent group
  foreach ($children_by_parent as $parentid => $children) {
    mtrace("Processing parent ID: {$parentid} (" . count($children) . " children)");

    // Filter out only the country departments for this parent
    $countries_in_group = array_filter($children, function($dept) {
      return !empty($dept->is_country); // Check if is_country is true (1)
    });

    // If no countries or only one country in this group, no sorting needed among countries
    if (count($countries_in_group) <= 1) {
      mtrace("  Skipping parent ID: {$parentid} (0 or 1 country found).");
      continue;
    }

    mtrace("  Found " . count($countries_in_group) . " countries for parent ID: {$parentid}. Applying sort...");

    // 4. Use a Bubble Sort like approach with reorder_hierarchy_item
    // This repeatedly compares adjacent countries and swaps them if they are in the wrong alphabetical order.
    // It continues until no swaps are needed in a full pass, meaning they are sorted.
    $sorted = false;
    $pass_count = 0;
    $max_passes = count($countries_in_group) * count($countries_in_group); // Safety break

    while (!$sorted && $pass_count < $max_passes) {
      $pass_count++;
      $swapped_in_pass = false;

      // Fetch the *current* order of children for this parent in each pass, as it changes
      $current_children_ordered = $DB->get_records(
          'local_orgs_departments',
          ['parentid' => $parentid],
          'sortthread ASC',
          'id, fullname, is_country, sortthread' // Fetch necessary fields
      );

      // Filter to get only the country IDs in their *current* order
      $current_country_ids_in_order = [];
      $country_objects_map = []; // Keep objects for name comparison
      foreach ($current_children_ordered as $child) {
        if (!empty($child->is_country)) {
          $current_country_ids_in_order[] = $child->id;
          $country_objects_map[$child->id] = $child;
        }
      }

      // Compare adjacent countries in the current order
      for ($i = 0; $i < count($current_country_ids_in_order) - 1; $i++) {
        $id1 = $current_country_ids_in_order[$i];
        $id2 = $current_country_ids_in_order[$i + 1];

        $dept1 = $country_objects_map[$id1];
        $dept2 = $country_objects_map[$id2];

        // If dept1 should come after dept2 alphabetically, swap them (move dept2 up)
        if (strnatcasecmp($dept1->fullname, $dept2->fullname) > 0) {
          mtrace("    Swapping '{$dept1->fullname}' (ID: {$id1}) and '{$dept2->fullname}' (ID: {$id2})");
          // Use the hierarchy function to move the second item ABOVE the first one
          $success = $department_hierarchy->reorder_hierarchy_item($id2, HIERARCHY_ITEM_ABOVE);
          if ($success) {
            $swapped_in_pass = true;
            // IMPORTANT: Break the inner loop and restart the pass,
            // because the order has changed.
            break;
          } else {
            // Throw an exception or log error if reorder fails
            throw new \moodle_exception('errorreorder', 'local_organisation', '', null, "Failed to reorder department ID {$id2} above {$id1}.");
          }
        }
      } // End for loop comparing adjacent countries

      // If no swaps occurred in this pass, the list is sorted
      if (!$swapped_in_pass) {
        $sorted = true;
      }
    } // End while not sorted

    if ($pass_count >= $max_passes) {
      mtrace("  Warning: Max passes reached for parent ID {$parentid}. Sorting may be incomplete.");
    } else if ($pass_count > 0) {
      mtrace("  Sorting completed for parent ID: {$parentid} in {$pass_count} passes.");
    } else {
      mtrace("  Countries already sorted for parent ID: {$parentid}.");
    }

  } // End foreach parent group

  // 5. Commit transaction
  $transaction->allow_commit();
  mtrace('Successfully finished sorting country departments alphabetically within each parent using hierarchy functions.');

} catch (Exception $e) {
  $transaction->rollback($e);
  mtrace('Error sorting country departments: ' . $e->getMessage());
  mtrace('Stack trace: ' . $e->getTraceAsString());
  // Consider more specific error handling or logging if needed
  exit(1); // Indicate failure
}

exit(0); // Indicate success
