<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  find_wrongpath_hierarchy_items.php description here.
 *
 * @package
 * @copyright  2025 <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';

$json = file_get_contents(__DIR__ . '/hierarchy.json');
$datas = json_decode($json, true);
$output_of_wrong_itemids = [];
foreach ($datas as $data) {
  if (strpos($data['path'], '/x') === false) {
    $output_of_wrong_itemids[] = $data['id'];
  }
}


print_object($output_of_wrong_itemids);