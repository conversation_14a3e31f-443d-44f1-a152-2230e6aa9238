<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>odle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_department.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/*
 * This script should bring the department from old table to new table
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';

global $DB;

// Start the transaction
$transaction = $DB->start_delegated_transaction();
try {
  // Prepare the insert query to copy data from mdl_enphase_org to mdl_local_orgs_departments
  $sql = "
        INSERT INTO {local_orgs_positions} 
            (
             id,
             shortname,
             idnumber,
             description,
             frameworkid,
             path,
             visible, 
             timecreated,
             timemodified,
             usermodified,
             fullname,
             parentid,
             depthlevel,
             sortthread,
             enphasesync,
             permissions)
        SELECT 
            id,
            shortname,
            idnumber,
            description,
            frameworkid,
            path,
            visible, 
            timecreated,
            timemodified,
            usermodified,
            fullname,
            parentid,
            depthlevel,
            sortthread,
            enphasesync, 
            '' AS permissions  -- Default value for the 'permissions' column (you can change this logic)
        FROM {enphase_pos}";

  // Execute the query
  $DB->execute($sql);

  // Commit the transaction if everything was successful
  $transaction->allow_commit();

  // Optional: Print success message
  echo "Data transfer complete!";

} catch (Exception $e) {
  // Log the error message
  echo "Error during data transfer: " . $e->getMessage();
  // Rollback the transaction in case of any error
  $transaction->rollback($e);

}