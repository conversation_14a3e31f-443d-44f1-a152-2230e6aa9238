<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>odle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_job_assignments.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
global $DB;

$transaction = $DB->start_delegated_transaction();
try {
  // Prepare the insert query to copy data from mdl_job_assignment to mdl_local_orgs_jobs
  $sql = "INSERT INTO {local_orgs_jobs} 
            (
             id,
             userid, 
             idnumber, 
             startdate,
             enddate,
             timecreated, 
             timemodified, 
             usermodified, 
             positionid, 
             departmentid, 
             sortorder, 
             pending_reporting_line_dep, 
             pending_reporting_line_pos, 
             departmentline_action,
             positionline_action,
             enphasesync,
             synctimemodified
            )
        SELECT 
            id,
            userid, 
            idnumber, 
            COALESCE(startdate, UNIX_TIMESTAMP()) as startdate, 
            COALESCE(enddate, 0) as enddate, 
            timecreated, 
            timemodified, 
            usermodified, 
            positionid, 
            organisationid, 
            sortorder, 
            'n' AS pending_reporting_line_dep, 
            'n' AS pending_reporting_line_pos, 
            'n' AS departmentline_action,
            'n' AS positionline_action,
            enphasesync, 
            synctimemodified
        FROM {job_assignment}";

  // Execute the query
  $DB->execute($sql);

  // Now we need to mark the department lead job assignment to mark as to be created
  // Ah wait it can be marked to be created by making a position as department lead

  // Commit the transaction if everything was successful
  $transaction->allow_commit();

  // Optional: Print success message
  echo "Data transfer complete!";

} catch (Exception $e) {
  // Log the error message
  echo "Error during data transfer: " . $e->getMessage();
  // Rollback the transaction in case of any error
  $transaction->rollback($e);
}