<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use local_organisation\country_branding\flag;

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
global $DB;

$transaction = $DB->start_delegated_transaction();

$old_country_records = $DB->get_records('local_geography_country', []);

$context_system = context_system::instance();
$fs = get_file_storage();
$filemanageroptions = flag::get_flag_file_options();
$mainadminuser = get_admin();

try {
  foreach ($old_country_records as $old_country_record) {
    // get the country code
    $country_code = $old_country_record->iso2;
    $country_id = $old_country_record->id;

    $newcountry = $DB->get_record('local_orgs_departments', ['is_country' => '1', 'idnumber' => $country_code]);
    if (!$newcountry){
      continue;
    }

    $comparepagename = $DB->sql_compare_text('pagename');
    $comparepagenameplaceholder = $DB->sql_compare_text(':pagename');
    $comparecountry_code = $DB->sql_compare_text('country_code');
    $comparecountry_codeplaceholder = $DB->sql_compare_text(':country_code');

    $sql = "SELECT * 
            FROM {local_multitenancyui_components} 
            WHERE {$comparepagename} = {$comparepagenameplaceholder}
                  AND {$comparecountry_code} = {$comparecountry_codeplaceholder}";
    $params = ['country_code' => $country_code, 'pagename' => 'faq'];
    $country_faqs = $DB->get_record_sql($sql, $params);
    // get the country faqs
    //$country_faqs = $DB->get_record('local_multitenancyui_components', ['country_code' => $country_code, 'pagename' => 'faq']);
    if (!$country_faqs){
      continue;
    }
    $base64faqdata = $country_faqs->page_content;
    $faqobject = unserialize(base64_decode($base64faqdata));
    $iterations = $faqobject->no_of_faq;

    $faq = new stdClass();

    for ($i = 1; $i <= $iterations; $i++) {
      $faq->departmentid = $newcountry->id;
      $faq->usermodified = $mainadminuser->id;
      $faq->timecreated = time();
      $faq->timemodified = time();

      $title_element = 'cardtitle_' . $i;
      $content_element = 'cardbodycontent_' . $i;

      $faq->question = $faqobject->{$title_element}['text'];
      $faq->answer = $faqobject->{$content_element}['text'];
      $DB->insert_record('local_orgs_country_faqs', $faq);
    }
  }
  $transaction->allow_commit();
}catch (Exception $e) {
    $transaction->rollback($e);
}

mtrace('Faqs copied successfully');