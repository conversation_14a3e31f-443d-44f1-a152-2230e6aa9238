<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  fix_hierarchy.php description here.
 *
 * @package
 * @copyright  2025 Krishna <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use local_organisation\hierarchy\department;

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
global $DB;

class fix_hierarchy
{
  private $items = [];
  private $orphanedItems = [];
  private $invalidPaths = [];

  /**
   * Validate hierarchy items
   *
   * @param array $items Array of hierarchy items with 'id', 'parent_id', and 'path' properties
   * @return array Results containing invalid items
   */
  public function validate(array $items): array
  {
    $this->items = $items;
    $this->findInvalidPaths();
    $this->findOrphanedItems();

    return [
        'invalid_paths' => $this->invalidPaths,
        'orphaned_items' => $this->orphanedItems
    ];
  }

  /**
   * Find items with invalid paths
   */
  private function findInvalidPaths(): void
  {
    foreach ($this->items as $item) {
      if (!$this->validatePath($item)) {
        $this->invalidPaths[] = $item;
      }
    }
  }

  /**
   * Validate a single item's path
   */
  private function validatePath(stdClass $item): bool
  {
    // If no parent_id, path should be just the item's ID
    if (!$item->parent_id) {
      return $item->path == '/'. $item->id;
    }

    // Find parent item
    $parent = $this->findItemById($item->parent_id);
    if (!$parent) {
      return false;
    }

    // Expected path should be parent's path + current item's ID
    $expectedPath = $parent->path . '/' . $item->id;
    return $item->path == $expectedPath;
  }

  /**
   * Find orphaned items (items with non-existent parent)
   */
  private function findOrphanedItems(): void
  {
    foreach ($this->items as $item) {
      if ($item->parent_id) {
        $parent = $this->findItemById($item->parent_id);
        if (!$parent) {
          $this->orphanedItems[] = $item;
        }
      }
    }
  }

  /**
   * Find item by ID
   */
  private function findItemById(int $id): ?stdClass
  {
    foreach ($this->items as $item) {
      if ($item->id == $id) {
        return $item;
      }
    }
    return null;
  }

  /**
   * Generate correct path for an item
   */
  public function generateCorrectPath(stdClass $item): string
  {
    if (!$item->parent_id) {
      return '/'.$item->id;
    }

    $parent = $this->findItemById($item->parent_id);
    if (!$parent) {
      return (string)$item->id;
    }

    return $parent->path . '/' . $item->id;
  }

  public function getHierarchyItems(): array {
    global $DB;
    $sql = "SELECT id, parentid as parent_id, path FROM {local_orgs_departments}";
    return $DB->get_records_sql($sql);
  }

  public function reinitialiseReportingLine(int $departmentid): void {
    $department_hierarchy = department::load_hierarchy();
    try {
      // 2. Create a ReflectionObject from your existing object instance
      //    Alternatively, you could use new ReflectionClass(get_class($departmentObject));
      $reflectionObject = new ReflectionObject($department_hierarchy);
      $protectedMethodName = 'update_reporting_line';
      $methodArguments = [$departmentid];

      // 3. Get the ReflectionMethod object for the protected method
      if (!$reflectionObject->hasMethod($protectedMethodName)) {
        echo "Error: Method '{$protectedMethodName}' does not exist on the object.\n";
        exit;
      }
      $methodToCall = $reflectionObject->getMethod($protectedMethodName);

      // 4. Make the protected method accessible
      $methodToCall->setAccessible(true);

      // 5. Invoke the method on your specific object instance
      //    The first argument to invoke() is the object instance itself.
      //    Subsequent arguments are the method's parameters.
      $result = $methodToCall->invoke($department_hierarchy, ...$methodArguments);
      // Or, if you prefer to pass arguments as an array:
      // $result = $methodToCall->invokeArgs($departmentObject, $methodArguments);

      echo "Result from '{$protectedMethodName}': " . print_r($result, true) . "\n";

    } catch (ReflectionException $e) {
      echo "ReflectionException: " . $e->getMessage() . "\n";
    } catch (Throwable $e) { // Catch any other potential errors during invocation
      echo "Error during method invocation: " . $e->getMessage() . "\n";
    }
  }
}

// Usage example:
//$items = [
//    ['id' => 1, 'parent_id' => null, 'path' => '1'],
//    ['id' => 2, 'parent_id' => 1, 'path' => '1/2'],
//    ['id' => 3, 'parent_id' => 1, 'path' => '1/3'],
//    ['id' => 4, 'parent_id' => 2, 'path' => '1/2/4'],
//    ['id' => 5, 'parent_id' => 99, 'path' => '99/5'],        // Invalid - orphaned item
//    ['id' => 6, 'parent_id' => 2, 'path' => '1/3/6'],        // Invalid path
//    ['id' => 7, 'parent_id' => null, 'path' => '7/wrong'],   // Invalid root path
//];

$validator = new fix_hierarchy();
//$items = $validator->getHierarchyItems();

$json = file_get_contents(__DIR__ . '/hierarchy.json');
$datas = json_decode($json, true);
$output_of_wrong_items = [];
foreach ($datas as $data) {
  if (strpos($data['path'], '/x') !== false) {
    continue;
  }
  $output_of_wrong_items[] = $data;
}

$results = $validator->validate($items);


// Display results
echo "Items with invalid paths:\n";
foreach ($results['invalid_paths'] as $item) {
  $correctPath = $validator->generateCorrectPath($item);
  echo "Item ID: {$item->id}\n";
  echo "Current path: {$item->path}\n";
  echo "Correct path should be: {$correctPath}\n";
  echo "Now updating path for: {$item->id}\n";
  $DB->set_field('local_orgs_departments', 'path', $correctPath, array('id' => $item->id));
  echo "Now reinitialising reporting line for department: {$item->id}\n\n";
  $validator->reinitialiseReportingLine($item->id);
}

echo "Orphaned items:\n";
foreach ($results['orphaned_items'] as $item) {
  echo "Item ID: {$item->id} (references non-existent parent ID: {$item->parent_id})\n";
}

