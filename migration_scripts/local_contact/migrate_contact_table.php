<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_contact_table.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
require_once $CFG->libdir . '/upgradelib.php';

global $DB;
$dbman = $DB->get_manager();

// Changing type of field countryid on table local_contact to int.
$table = new xmldb_table('local_contact');
$field = new xmldb_field('countryid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'email');
// We need to replace country iso2 code to country id
//$sql = "UPDATE {local_contact} SET countryid = (SELECT id FROM {local_orgs_departments} WHERE idnumber = countryid)";
$sql = "UPDATE {local_contact} lc
        JOIN {local_orgs_departments} od ON od.idnumber = lc.countryid
        SET lc.countryid = od.id";

$DB->execute($sql);
if ($dbman->field_exists($table, $field)) {
  $dbman->change_field_type($table, $field);
}