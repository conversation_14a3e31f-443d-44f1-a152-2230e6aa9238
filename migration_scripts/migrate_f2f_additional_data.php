<?php

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

$sql = "SELECT 
  fs.id AS session, 
  fsd_shortname.data AS shortname,
  fsd_visible.data AS visible, 
  fsd_location.data AS location, 
  fsd_city.data AS city, 
  fsd_country.data AS country, 
  fsd_timezone.data AS timezone, 
  fsd_trainingroom.data AS trainingroom 
FROM `mdl_facetoface_sessions` fs 
  LEFT JOIN `mdl_facetoface_session_data` fsd_shortname ON fsd_shortname.sessionid=fs.id AND fsd_shortname.fieldid=8 
  LEFT JOIN `mdl_facetoface_session_data` fsd_visible ON fsd_visible.sessionid=fs.id AND fsd_visible.fieldid=10 
  LEFT JOIN `mdl_facetoface_session_data` fsd_location ON fsd_location.sessionid=fs.id AND fsd_location.fieldid=6 
  LEFT JOIN `mdl_facetoface_session_data` fsd_city ON fsd_city.sessionid=fs.id AND fsd_city.fieldid=5 
  LEFT JOIN `mdl_facetoface_session_data` fsd_country ON fsd_country.sessionid=fs.id AND fsd_country.fieldid=9 
  LEFT JOIN `mdl_facetoface_session_data` fsd_timezone ON fsd_timezone.sessionid=fs.id AND fsd_timezone.fieldid=4 
  LEFT JOIN `mdl_facetoface_session_data` fsd_trainingroom ON fsd_trainingroom.sessionid=fs.id AND fsd_trainingroom.fieldid=7";

try{
  $transaction = $DB->start_delegated_transaction();
  $recordset = $DB->get_recordset_sql($sql);

  foreach($recordset as $record) {
    \mod_facetoface\helper\f2f_sess_req_data_helper::save($record);
  }
  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
