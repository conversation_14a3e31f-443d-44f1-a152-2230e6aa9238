<?php
/*
 * Note: This script should run after all the organisation/departments has been created.
 *
 * This script should update country_code in learning_plan_availability table.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;


$update_sql = "UPDATE {preapprovedcountries} pc
JOIN {local_orgs_departments} dept ON pc.ccode = dept.idnumber AND dept.is_country = 1
SET pc.ccode = dept.id";

$sql = "SELECT pc.id, dept.id AS dept_id FROM {preapprovedcountries} pc
        JOIN {local_orgs_departments} dept ON pc.ccode = dept.idnumber AND dept.is_country = 1";


try{
  $transaction = $DB->start_delegated_transaction();
  $recordset = $DB->get_recordset_sql($sql);

  foreach($recordset as $record) {
    $data = new stdClass();
    $data->id = $record->id;
    $data->ccode = $record->dept_id;

    $DB->update_record("preapprovedcountries", $data);
  }
  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
