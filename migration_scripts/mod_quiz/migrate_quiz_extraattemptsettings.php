<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
global $DB;

$sql = "SELECT id, taketoitemid FROM {quiz} WHERE taketocontextlevel = ". CONTEXT_MODULE ." AND assesmenttype = 2";
$records = [];
try{
  $records = $DB->get_records_sql($sql);
}catch (Exception $e){
  print_object($e);
  // it means we don't have the fields we are trying to get in the query
  return true;
}

foreach ($records as $record) {
  $record->quizid = $record->id;
  $record->taketocmid = $record->taketoitemid;
  if (!$record->taketocmid){
    continue;
  }
  unset($record->taketoitemid);
  unset($record->id);
  if (!$DB->record_exists('quizaccess_extraattempts', (array)$record)){
    $DB->insert_record('quizaccess_extraattempts', $record);
  }
}

// @todo need to setup reset attempt for course assessment
$sql = "SELECT id, taketoitemid FROM {quiz} WHERE taketocontextlevel = ". CONTEXT_MODULE ." AND assesmenttype = 1";
$records = [];
try{
  $records = $DB->get_records_sql($sql);
}catch (Exception $e){
  print_object($e);
  // it means we don't have the fields we are trying to get in the query
  return true;
}

foreach ($records as $record) {
  if ($record->taketoitemid) {
    $record->taketocmid = $record->taketoitemid;
  }else{
    $record->taketocmid = 0;
  }
  $record->quizid = $record->id;
  unset($record->taketoitemid);
  unset($record->id);

  if (!$DB->record_exists('quizaccess_extraattempts', (array)$record)){
    $DB->insert_record('quizaccess_extraattempts', $record);
  }
}

mtrace('Faqs copied successfully');