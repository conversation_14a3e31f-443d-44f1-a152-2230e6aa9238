<?php

define('CLI_SCRIPT', true);

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/filelib.php');
require_once($CFG->libdir . '/adminlib.php');

cron_setup_user();

// Define the folder path where your images are stored
$images_folder = '/Users/<USER>/Downloads/606_course_images_16-9-ratio_png_ext/'; // Update this path

// Get all image files from the folder
$image_files = glob($images_folder . "*.png");

$fs = get_file_storage();
$success_count = 0;
$error_count = 0;

foreach ($image_files as $image_path) {
  // Extract course ID from filename (assuming filenames are like "1.jpg", "2.jpg")
  $course_id = (int) pathinfo($image_path, PATHINFO_FILENAME);

  if (!in_array($course_id, [258,259,260])){
    continue;
  }

  // Verify if course exists
  if (!$course = $DB->get_record('course', array('id' => $course_id))) {
    echo "Course ID {$course_id} not found.<br>";
    $error_count++;
    continue;
  }

  try {
    // Prepare file record
    $context = context_course::instance($course_id);

    // Get existing files for this LP
    $existing_files = $fs->get_area_files(
        $context->id,
        'course',
        'overviewfiles',
        0,
        'filesize DESC',
    );

    // Store backup of existing files
    $backup_files = [];
    foreach ($existing_files as $file) {
      $backup_files[] = [
          'content' => $file->get_content(),
          'filename' => $file->get_filename(),
          'fileinfo' => [
              'contextid' => $context->id,
              'component' => 'course',
              'filearea' => 'overviewfiles',
              'itemid' => 0,
              'filepath' => '/',
              'filename' => $file->get_filename()
          ]
      ];
    }

    // Prepare file record
    $fileinfo = array(
        'contextid' => $context->id,
        'component' => 'course',
        'filearea' => 'overviewfiles',
        'itemid' => 0,
        'filepath' => '/',
        'filename' => basename($image_path)
    );

    // Create new file
    $content = file_get_contents($image_path);
    if ($content !== false) {
      // Delete existing course overview files
      $fs->delete_area_files($context->id, 'course', 'overviewfiles');
      $fs->create_file_from_string($fileinfo, $content);
      echo "Successfully updated image for course ID: {$course_id}<br>";
      $success_count++;
    } else {
      echo "Failed to read image file for course ID: {$course_id}<br>";
      $error_count++;
    }

  } catch (Exception $e) {
    echo "Error processing course ID {$course_id}: " . $e->getMessage() . "<br>";
    $error_count++;
    // Restore original files
    foreach ($backup_files as $backup) {
      try {
        $fs->create_file_from_string($backup['fileinfo'], $backup['content']);
      } catch (Exception $restore_e) {
        $errors[] = "Error restoring original file for Course ID {$course_id}: " .
            $restore_e->getMessage();
      }
    }
  }
}

echo "<br>Process completed.<br>";
echo "Successfully updated: {$success_count} courses<br>";
echo "Errors encountered: {$error_count} courses<br>";
