<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  course_image_copy.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once(__DIR__ . '/../../config.php');
//require_once($CFG->libdir . '/coursecatlib.php');
require_once($CFG->dirroot.'/course/lib.php');

cron_setup_user();

// Create the course_images directory if it doesn't exist
$image_directory = $CFG->dataroot . '/course_images/';
if (!file_exists($image_directory)) {
    mkdir($image_directory, 0755, true);
}

// Get all courses
$courses = get_courses();

foreach ($courses as $course) {
    // Create a course_in_list object
    $courseobj = new core_course_list_element($course);

    // Get course overview files
    $overviewfiles = $courseobj->get_course_overviewfiles();

    foreach ($overviewfiles as $file) {
        if ($file->is_valid_image()) {
            // Generate a unique filename using course ID
            $new_filename = 'course_' . $course->id . '_' . $file->get_filename();
            $filepath = $image_directory . $new_filename;

            try {
                // Get the file content
                $content = $file->get_content();

                // Save the file
                if (file_put_contents($filepath, $content)) {
                    echo "Successfully saved image for course ID {$course->id}: {$new_filename}<br>";
                } else {
                    echo "Failed to save image for course ID {$course->id}<br>";
                }
            } catch (Exception $e) {
                echo "Error processing image for course ID {$course->id}: " . $e->getMessage() . "<br>";
            }

            // Only save the first valid image
            break;
        }
    }
}

echo "Process completed!";


