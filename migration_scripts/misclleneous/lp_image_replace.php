<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  lp_image_copy.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/filelib.php');
require_once($CFG->libdir . '/adminlib.php');

// Ensure the user has appropriate permissions
cron_setup_user();

// Define the folder path where your new images are stored
$new_images_folder = '/Users/<USER>/Downloads/enphase/assets/lp_images_test/'; // Update this path

// Get all image files from the folder
$image_files = glob($new_images_folder . "*.png");

$fs = get_file_storage();
$success_count = 0;
$errors = array();

foreach ($image_files as $image_path) {
  // Extract LP ID from filename (assuming filenames are like "1.jpg", "2.jpg")
  $lp_id = (int) pathinfo($image_path, PATHINFO_FILENAME);

  try {
    // Get the context
    $context = context_system::instance();

    // Get existing files for this LP
    $existing_files = $fs->get_area_files(
        $context->id,
        'block_learning_plan',
        'lp_image_'.$lp_id,
        0,
        'filesize DESC',
    );

    // Store backup of existing files
    $backup_files = [];
    foreach ($existing_files as $file) {
      $backup_files[] = [
          'content' => $file->get_content(),
          'filename' => $file->get_filename(),
          'fileinfo' => [
              'contextid' => $context->id,
              'component' => 'block_learning_plan',
              'filearea' => 'lp_image_'.$lp_id,
              'itemid' => 0,
              'filepath' => '/',
              'filename' => $file->get_filename()
          ]
      ];
    }

    // Read new image content
    $new_content = file_get_contents($image_path);
    if ($new_content === false) {
      throw new Exception("Failed to read new image file");
    }

    // Prepare new file record
    $fileinfo = array(
        'contextid' => $context->id,
        'component' => 'block_learning_plan',
        'filearea' => 'lp_image_'.$lp_id,
        'itemid' => 0,
        'filepath' => '/',
        'filename' => basename($image_path)
    );

    // Start transaction
    $transaction = $DB->start_delegated_transaction();

    try {
      // Delete existing files for this LP
      $fs->delete_area_files($context->id, 'block_learning_plan', 'lp_image_'.$lp_id, 0);

      // Create new file
      $fs->create_file_from_string($fileinfo, $new_content);

      $transaction->allow_commit();
      $success_count++;
      echo "Successfully replaced image for Learning Plan ID: {$lp_id}<br>";

    } catch (Exception $inner_e) {
      // Rollback the transaction
      $transaction->rollback($inner_e);

      // Restore original files
      foreach ($backup_files as $backup) {
        try {
          $fs->create_file_from_string($backup['fileinfo'], $backup['content']);
        } catch (Exception $restore_e) {
          $errors[] = "Error restoring original file for LP ID {$lp_id}: " .
              $restore_e->getMessage();
        }
      }

      throw new Exception("Failed to update LP image: " . $inner_e->getMessage());
    }

  } catch (Exception $e) {
    $errors[] = "Error processing LP ID {$lp_id}: " . $e->getMessage();
  }
}

// Output results
echo "<br>Process completed.<br>";
echo "Successfully updated: {$success_count} learning plans<br>";

if (!empty($errors)) {
  echo "<br>Errors encountered:<br>";
  foreach ($errors as $error) {
    echo $error . "<br>";
  }
}
