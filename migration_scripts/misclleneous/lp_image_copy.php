<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  lp_image_copy.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define('CLI_SCRIPT', true);

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/filelib.php');

// Define target directory in moodledata
$target_dir = $CFG->dataroot . '/lp_images/';

// Create directory if it doesn't exist
if (!file_exists($target_dir)) {
  mkdir($target_dir, 0755, true);
}


// Create file storage instance
$fs = get_file_storage();

// get all the learning plan ids from the database
$lp_ids = $DB->get_records('learning_learningplan', [], 'id', 'id');

$context = context_system::instance();

foreach ($lp_ids as $lp_id) {
    $id = $lp_id->id;
    // Define the component and filearea
    $component = 'block_learning_plan';
    $filearea = 'lp_image_'.$id;
    $contextid = $context->id;

    // Get all files from the file area
    $files = $fs->get_area_files(
        $contextid,
        $component,
        $filearea,
        0, // itemid
        'filesize DESC', // sort by filename
        false // include directories or not
    );


  foreach ($files as $file) {
    // Skip directories and dot files
    if ($file->is_directory() || $file->get_filename() === '.') {
      continue;
    }

    $filename = $file->get_filename();
    $filepath = $target_dir . $filename;

    try {
      // Get the content and write to new location
      $content = $file->get_content();
      if (file_put_contents($filepath, $content) !== false) {
        $copied_files++;
        echo "Successfully copied: {$filename}<br>";
      } else {
        $errors[] = "Failed to write file: {$filename}";
      }
    } catch (Exception $e) {
      $errors[] = "Error processing {$filename}: " . $e->getMessage();
    }
  }
}

// Output results
echo "<br>Copying complete.<br>";
echo "Successfully copied {$copied_files} files.<br>";

if (!empty($errors)) {
  echo "<br>Errors encountered:<br>";
  foreach ($errors as $error) {
    echo $error . "<br>";
  }
}