<?php

define('CLI_SCRIPT', true);

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/filelib.php');


//$fs = get_file_storage();
//$context = context_system::instance();
//// Delete all files from systemfiles area
//$files = $fs->delete_area_files(
//    $context->id,
//    'catalogitem_tr',
//    'systemfiles',
//    false, // false to get files for all itemids
//);
//
//die;



function export_system_files_to_json() {
  global $CFG;

  $context = context_system::instance();
  $fs = get_file_storage();

  // Get all files from systemfiles area
  $files = $fs->get_area_files(
      $context->id,
      'catalogitem_tr',
      'systemfiles',
      false, // false to get files for all itemids
      'filesize',
      false
  );

  $file_data = array();
  foreach ($files as $file) {
    // Skip directories and files named '.'
    if ($file->is_directory() || $file->get_filename() === '.') {
      continue;
    }

    // Group files by their itemid (system_id)
    $system_id = $file->get_itemid();
    if (!isset($file_data[$system_id])) {
      $file_data[$system_id] = array();
    }

    $file_data[$system_id][] = array(
        'filename' => $file->get_filename(),
        'content' => base64_encode($file->get_content()),
        'mimetype' => $file->get_mimetype(),
        'userid' => $file->get_userid(),
        'timemodified' => $file->get_timemodified(),
        'filepath' => $file->get_filepath()
    );
  }

  // Create export directory if it doesn't exist
  $export_dir = $CFG->dataroot . '/temp/file_exports';
  if (!is_dir($export_dir)) {
    mkdir($export_dir, 0755, true);
  }

  // Save to JSON file with timestamp
  $timestamp = date('Y-m-d_His');
  $json_file = $export_dir . '/system_files_export_' . $timestamp . '.json';
  $json_content = json_encode($file_data, JSON_PRETTY_PRINT);
  file_put_contents($json_file, $json_content);

  return array(
      'file_path' => $json_file,
      'total_systems' => count($file_data),
      'total_files' => array_sum(array_map('count', $file_data))
  );
}




function import_system_files_from_json($json_file_path) {
  global $CFG;

  // Validate file exists
  if (!file_exists($json_file_path)) {
    throw new file_exception('JSON file not found');
  }

  // Read and decode JSON
  $json_content = file_get_contents($json_file_path);
  $all_file_data = json_decode($json_content, true);

  //print_object($all_file_data);
  //die;

  if (json_last_error() !== JSON_ERROR_NONE) {
    throw new file_exception('Invalid JSON format');
  }

  $context = context_system::instance();
  $fs = get_file_storage();
  $temp_dir = make_temp_directory('transfer');

  $results = array(
      'success_count' => 0,
      'error_count' => 0,
      'systems_processed' => 0,
      'errors' => array()
  );

  foreach ($all_file_data as $system_id => $files) {
    try {
      foreach ($files as $file) {
        try {
          // Validate file data
          if (!isset($file['filename']) || !isset($file['content'])) {
            throw new Exception('Missing required file data');
          }

          $temp_path = $temp_dir . '/' . $file['filename'];

          // Decode and save content to temp file
          $file_content = base64_decode($file['content']);
          if ($file_content === false) {
            throw new Exception('Invalid base64 content');
          }

          file_put_contents($temp_path, $file_content);

          // Prepare file record
          $file_record = array(
              'contextid' => $context->id,
              'component' => 'catalogitem_tr',
              'filearea'  => 'systemfiles',
              'itemid'    => $system_id,
              'filepath'  => $file['filepath'] ?? '/',
              'filename'  => $file['filename'],
              'userid'    => $file['userid'] ?? 0,
              'mimetype'  => $file['mimetype'] ?? null,
              'timemodified' => $file['timemodified'] ?? time()
          );

          // Check if file already exists
          if (!$fs->file_exists(
              $context->id,
              'catalogitem_tr',
              'systemfiles',
              $system_id,
              $file_record['filepath'],
              $file['filename']
          )) {
            $fs->create_file_from_pathname($file_record, $temp_path);
            $results['success_count']++;
          }

          // Clean up temp file
          unlink($temp_path);

        } catch (Exception $e) {
          $results['error_count']++;
          $results['errors'][] = "Error processing system $system_id, file {$file['filename']}: " . $e->getMessage();
        }
      }
      $results['systems_processed']++;

    } catch (Exception $e) {
      $results['errors'][] = "Error processing system $system_id: " . $e->getMessage();
    }
  }

  // Clean up temp directory if empty
  if (is_dir($temp_dir) && count(scandir($temp_dir)) == 2) {
    rmdir($temp_dir);
  }

  return $results;
}


// On source server - export all system files
//try {
//  $export_result = export_system_files_to_json();
//  echo "Export completed:\n";
//  echo "File saved to: " . $export_result['file_path'] . "\n";
//  echo "Total systems: " . $export_result['total_systems'] . "\n";
//  echo "Total files: " . $export_result['total_files'] . "\n";
//} catch (Exception $e) {
//  echo "Export failed: " . $e->getMessage() . "\n";
//}


// On destination server - import files
try {
  //$json_file_path = $CFG->dataroot . '/temp/file_imports/system_files_export_2024-01-20_123456.json';
  $json_file_path = '/Users/<USER>/Downloads/enphase/migration_plan_helper/system_files_export_2025-04-18_113103.json';

  $import_results = import_system_files_from_json($json_file_path);

  echo "Import completed:\n";
  echo "Systems processed: " . $import_results['systems_processed'] . "\n";
  echo "Files successfully imported: " . $import_results['success_count'] . "\n";
  echo "Files failed: " . $import_results['error_count'] . "\n";

  if (!empty($import_results['errors'])) {
    echo "\nErrors encountered:\n";
    foreach ($import_results['errors'] as $error) {
      echo "- $error\n";
    }
  }
} catch (Exception $e) {
  echo "Import failed: " . $e->getMessage() . "\n";
}
