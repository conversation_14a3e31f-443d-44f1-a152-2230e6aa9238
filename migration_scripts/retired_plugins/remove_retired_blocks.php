<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  migrate_country_flags.php description here.
 *
 * @package
 * @copyright  2025  <>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


define('CLI_SCRIPT', true);

require_once __DIR__ . '/../../config.php';
require_once($CFG->libdir . '/clilib.php');
require_once($CFG->libdir . '/adminlib.php');
global $DB;

cron_setup_user();

// @todo remove block todo
// @todo remove block myprogress
// @todo remove block user learning
// @todo remove block user key activity

$options['plugins'] = 'block_my_progress, block_todo, block_user_learning, block_key_activity';

$pluginman = core_plugin_manager::instance();
$plugininfo = $pluginman->get_plugins();

if ($options['plugins']) {
  $components = explode(',', $options['plugins']);
  foreach ($components as $component) {
    $plugin = $pluginman->get_plugin_info($component);

    if (is_null($plugin)) {
      cli_writeln('Unknown plugin: ' . $component);
    } else {
      $pluginstring = $plugin->component . "\t" . $plugin->displayname;

      if ($pluginman->can_uninstall_plugin($plugin->component)) {
        if ($options['run']) {
          cli_writeln('Uninstalling: ' . $pluginstring);
          $progress = new progress_trace_buffer(new text_progress_trace(), true);
          $pluginman->uninstall_plugin($plugin->component, $progress);
          $progress->finished();
          cli_write($progress->get_buffer());
        } else {
          cli_writeln('Will be uninstalled: ' . $pluginstring);
        }
      } else {
        cli_writeln('Can not be uninstalled: ' . $pluginstring);
      }
    }
  }
  mtrace('Plugins uninstalled successfully');
  exit(0);
}

