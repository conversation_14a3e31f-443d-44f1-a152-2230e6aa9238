<?php

/*
 * Note: This script should run after migrate_training_courses.php and migrate_learningplan_restrictionset_courses_table.php script.
 *
 * This script should change the enrolment method of enrolled users in courses based on learning plan enrolment
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

//die();

global $DB;

$sql = "SELECT lprc.courseid cid, lp.id as lpid FROM {learning_learningplan} lp
        JOIN {learningplan_restrictionset} lpr ON lp.id=lpr.learningplan_id
        JOIN {lp_restrictionset_courses} lprc ON lprc.restrictionsetid = lpr.id
        JOIN {course} c ON c.id=lprc.courseid
        UNION
        SELECT lpsi.course_id as cid, lp.id as lpid  FROM {learning_learningplan} lp
        JOIN {lp_sets} lps ON lp.id=lps.lpid
        JOIN {lp_set_items} lpsi ON lpsi.lpset_id = lps.id
        JOIN {course} c ON c.id=lpsi.course_id
        ORDER BY lpid";

$records = $DB->get_records_sql($sql);

$plugin_config = get_config('block_learning_plan');
$roleid = $plugin_config->role_cpc_request;

$old_lpid=0;

$enrol_lp = enrol_get_plugin('lp');

$enrol_manual = enrol_get_plugin('manual');

foreach ($records as $record) {
  $lpid = $record->lpid;
  $courseid = $record->cid;
  if($old_lpid != $lpid) {

    $instances = enrol_get_instances($courseid, true);

    $found_lp_instance = false;
    foreach ($instances as $instance) {
      if ($instance->enrol=='lp' && isset($instance->customint1) && $instance->customint1 == $lpid) {
        // An instance with the specified customint1 exists, so we do nothing
        $lp_instance = $instance;
        $found_lp_instance = true;
        break;
      }
    }
    // If no existing instance is found, add a new one with customint1 set to $lpid

    if (!$found_lp_instance) {
      $fields = ['name' => 'Enrol lp - '.$lpid, 'customint1' => $lpid];
      $course = get_course($courseid);
      $instance_id = $enrol_lp->add_instance($course, $fields);

      $lp_instance = $DB->get_record('enrol',['id'=>$instance_id]);
    }

//    $users = $DB->get_records('learning_plan_requests', ['lpid'=>$lpid,'status'=>1],'','userid,lpid');

  }

//  $manual_instance = $DB->get_record('enrol', ['enrol' => 'manual', 'courseid' => $courseid, 'status' => ENROL_INSTANCE_ENABLED]);

//  foreach($users as $user) {
//
//    $enrol_lp->enrol_user($lp_instance, $user->userid, $roleid, 0, 0);
//
//    if($manual_instance) {
//      $enrol_manual->unenrol_user($manual_instance, $user->userid);
//    }
//  }

  $old_lpid = $lpid;

}
