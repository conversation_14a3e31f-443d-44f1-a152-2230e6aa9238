<?php
/*
 * Run this script only once
 * This script should run only after facetoface activity setting is properly set with showoncalendar and usercalentry.
 *
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

raise_memory_limit(MEMORY_UNLIMITED);


require_once __DIR__ . '/../mod/facetoface/lib.php';

global $DB, $CFG;

$f2f = $DB->get_records('facetoface');
foreach ($f2f as $facetoface) {
  echo "Facetoface id: ".$facetoface->id . "\n";

  if ($sessions = $DB->get_records('facetoface_sessions',['facetoface' => $facetoface->id])) {

    foreach ($sessions as $key => $value) {
      $sessions[$key]->sessiondates = facetoface_get_session_dates($value->id);
    }
    foreach ($sessions as $session) {
        facetoface_delete_user_calendar_events($session, 'booking');
        facetoface_delete_user_calendar_events($session, 'session');
        facetoface_remove_session_from_calendar($session, 0); // Session user event for session creator.
        facetoface_remove_session_from_calendar($session, $facetoface->course); // Session course event.
        facetoface_remove_session_from_calendar($session, SITEID); // Session site event.
    }


    foreach ($sessions as $session) {

        $users = facetoface_get_attendees($session->id);

        foreach ($users as $user) {

          $eventtype = $user->statuscode == MDL_F2F_STATUS_BOOKED ? 'booking' : 'session';


          //$result = facetoface_add_session_to_calendar($session, $facetoface, 'user', $user->id, $eventtype);
          //copying the function itself

          $calendartype = 'user';
          $userid = $user->id;


          if (empty($session->datetimeknown)) {
            return true; // Date unkown, can't add to calendar.
          }

          if (empty($facetoface->showoncalendar) && empty($facetoface->usercalentry)) {
            return true; // Facetoface calendar settings prevent calendar.
          }

          $description = '';
          if (!empty($facetoface->description)) {
            $description .= html_writer::tag('p', clean_param($facetoface->description, PARAM_CLEANHTML));
          }
          $description .= facetoface_print_session($session, false, true, true);
          $linkurl = new moodle_url('/mod/facetoface/signup.php', array('f' => $facetoface->id, 's' => $session->id));
          $linktext = get_string('signupforthissession', 'facetoface');

          if ($calendartype == 'site' && $facetoface->showoncalendar == F2F_CAL_SITE) {
            $courseid = SITEID;
            $modulename = '0';
            $description .= html_writer::link($linkurl, $linktext);
          } else if ($calendartype == 'course' && $facetoface->showoncalendar == F2F_CAL_COURSE) {
            $courseid = $facetoface->course;
            $modulename = 'facetoface';
            $description .= html_writer::link($linkurl, $linktext);
          } else if ($calendartype == 'user' && $facetoface->usercalentry) {
            $courseid = 0;
            $modulename = '0';
            $urlvar = ($eventtype == 'session') ? 'attendees' : 'signup';
            $linkurl = $CFG->wwwroot . "/mod/facetoface/" . $urlvar . ".php?s=$session->id";
            $description .= get_string("calendareventdescription{$eventtype}", 'facetoface', $linkurl);
          } else {
            return true;
          }

          /* $shortname = $facetoface->shortname;
                if (empty($shortname)) {
                    $shortname = substr($facetoface->name, 0, CALENDAR_MAX_NAME_LENGTH);
          */
          // Added to get session short name in calendar instead of module shortname
          $sessionshortname = $DB->get_record_sql("SELECT d.data FROM mdl_facetoface_session_field f
		        	JOIN mdl_facetoface_session_data d on d.fieldid = f.id
		        	WHERE f.shortname = 'shortname' and d.sessionid=$session->id");
          $shortname = $sessionshortname->data;
          if (empty($shortname)) {
            $shortname = $facetoface->shortname;
            if (empty($shortname)) {
              $shortname = substr($facetoface->name, 0, CALENDAR_MAX_NAME_LENGTH);
            }
          }

          $result = true;
          foreach ($session->sessiondates as $date) {
            $newevent = new stdClass();
            $newevent->name = $shortname;
            $newevent->description = $description;
            $newevent->format = FORMAT_HTML;
            $newevent->courseid = $courseid;
            $newevent->groupid = 0;
            $newevent->userid = $userid;
            $newevent->uuid = "{$session->id}";
            $newevent->instance = $session->facetoface;
            $newevent->modulename = $modulename;
            $newevent->eventtype = "facetoface{$eventtype}";
            $newevent->type = 0; // CALENDAR_EVENT_TYPE_STANDARD: Only display on the calendar, not needed on the block_myoverview.
            $newevent->timestart = $date->timestart;
            $newevent->timeduration = $date->timefinish - $date->timestart;
            $newevent->visible = 1;
            $newevent->timemodified = time();


            $result = $result && $DB->insert_record('event', $newevent);
          }
        }
    }
  }
}
echo "Everything is done!";
