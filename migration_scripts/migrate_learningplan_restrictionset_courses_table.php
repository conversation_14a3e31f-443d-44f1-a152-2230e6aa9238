<?php
/*
 * Run this script only once
 * This script is rename mdl_learningplan_restrictionset_courses to mdl_lp_restrictionset_courses.
 *
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

$dbman = $DB->get_manager();

$table1 = new xmldb_table('lp_restrictionset_courses');
$table2 = new xmldb_table('learningplan_restrictionset_courses');

if ($dbman->table_exists($table2)) {

  if($dbman->table_exists($table1)) {
    $dbman->drop_table($table1);
  }
  $dbman->rename_table($table2, 'lp_restrictionset_courses');
  echo 'Task completed successfully.' . PHP_EOL;
} else {
  echo 'Already done.' . PHP_EOL;
}
