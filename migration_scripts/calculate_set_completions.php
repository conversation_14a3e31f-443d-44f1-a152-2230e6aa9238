<?php
/*
 * Run this script only after migrate_training_course_completion.php and migrate_training_courses.php
 * This script should calculate set completion on set item completion basis and store it in set_completions table.
 *
 */

use block_learning_plan\completion_check;
use block_learning_plan\helper\lp_sets_helper;

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;



$completion_count_sql = "SELECT lpsic.userid,lpsi.lpset_id,COUNT(lpsi.id) AS completed_item_count FROM {lp_set_item_completion} lpsic
        JOIN {lp_set_items} lpsi ON lpsic.item_id=lpsi.id
        JOIN {course} c ON lpsi.course_id=c.id
        JOIN {lp_sets} lps ON lps.id = lpsi.lpset_id
        GROUP BY lpsic.userid,lpsi.lpset_id";

$require_count_sql = "SELECT lpsi.lpset_id,COUNT(lpsi.id) AS total_item_count FROM {lp_set_items} lpsi
        JOIN {course} c ON lpsi.course_id=c.id
        JOIN {lp_sets} lps ON lps.id = lpsi.lpset_id
        GROUP BY lpsi.lpset_id";

$time = time();

try{
  $transaction = $DB->start_delegated_transaction();
  $required_items_count = $DB->get_records_sql_menu($require_count_sql);
  $recordset = $DB->get_recordset_sql($completion_count_sql);

  $insert_sql = "INSERT INTO `mdl_lp_set_completion` 
  (`set_id`, `userid`, `progress`, `status`,`timecompleted`,`usermodified`,`timecreated`,`timemodified`) VALUES ";

  $values_sql = [];
  $counter = 0;

  foreach($recordset as $record) {

    $lpset_id = $record->lpset_id;
    $userid = $record->userid;
    $require_count = $required_items_count[$lpset_id];
    $completed_count = $record->completed_item_count;

    if(!empty($require_count) && $require_count > 0) {

      if($completed_count >= $require_count) {
        $progress = 100;
//        echo "userid {$userid} has completed lpset_id {$lpset_id} completely.\n";
      } else {
        $lp_set = lp_sets_helper::get($lpset_id);
        $progress = completion_check::check_set_completion($lp_set, $userid);
        $progress = (int)floor($progress['progress']);
//        echo "userid {$userid} has completed lpset_id {$lpset_id} {$progress} percent.\n";
      }
      $status = ($progress==100)?1:0;


//      $data = new stdClass();
//      $data->set_id= $lpset_id;
//      $data->userid = $userid;

//      $filter = (array) $data;

//      $data->progress = $progress;
//      $data->status = $status;
//      $data->timecompleted = $time;
//      $data->usermodified = 2;
//      $data->timecreated = $time;
//      $data->timemodified = $time;

//      lp_set_completion_helper::save($data, $filter);

      $values_sql[] = "({$lpset_id}, {$userid}, {$progress}, {$status}, {$time}, 2, {$time}, {$time})";

      $counter++;

    }

    if ($counter > 1000) {
      $values_sql_str = implode(',', $values_sql);
      $final_sql = $insert_sql . $values_sql_str;
      $DB->execute($final_sql);
      $counter = 0;
      $values_sql = [];
      $values_sql_str = '';
      $final_sql = '';
    }

  }
  $values_sql_str = implode(',', $values_sql);
  $final_sql = $insert_sql . $values_sql_str;

//  print_object($final_sql);

  $DB->execute($final_sql);

  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
