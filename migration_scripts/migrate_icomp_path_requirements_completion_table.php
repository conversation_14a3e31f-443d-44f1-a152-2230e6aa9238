<?php
/*
 * Run this script only once
 * This script is rename mdl_standalone_course_requests to mdl_tool_sc_requests.
 *
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

$dbman = $DB->get_manager();

$table1 = new xmldb_table('icomp_path_req_completion');
$table2 = new xmldb_table('icomp_path_requirements_completion');

if ($dbman->table_exists($table2)) {

  if($dbman->table_exists($table1)) {
    $dbman->drop_table($table1);
  }
  $dbman->rename_table($table2, 'icomp_path_req_completion');
  echo 'Task completed successfully.' . PHP_EOL;
} else {
  echo 'Already done.' . PHP_EOL;
}
