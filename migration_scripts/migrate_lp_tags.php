<?php

use block_learning_plan\custom_context\context_program;
/*
 * Note: This script should run only once.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

$sql = "SELECT lpid, CONCAT_WS(',', role, product) AS tags FROM {local_cm_lp_tags} WHERE role IS NOT NULL OR product IS NOT NULL";

$lp_tags = $DB->get_records_sql_menu($sql);

foreach ($lp_tags as $key=>$lp_tag) {
  core_tag_tag::set_item_tags(
    'block_learning_plan',
    'learning_learningplan',
    $key,
    context_program::instance($key),
    tags_arr($lp_tag)
  );
}
function tags_arr($lp_tag)
{
  $tags = explode(',', $lp_tag);
  return array_filter($tags, function($value) {
    return $value !== "";
  });
}
