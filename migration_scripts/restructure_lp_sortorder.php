<?php

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;

$sql = "SELECT id, sortorder, timecreated FROM `mdl_learning_learningplan` ORDER BY sortorder DESC, timeadded ASC";
try{
  $recordset = $DB->get_recordset_sql($sql);
  $sortorder = 1;
  foreach ($recordset as $record) {
    $data = new stdClass();
    $data->sortorder = $sortorder;
    $data->id = $record->id;
    $DB->update_record("learning_learningplan", $data);
    $sortorder++;
  }
  echo "Everything is done!";
} catch (Exception $e){
  echo "Something went wrong!";
  print_object($e);
}
