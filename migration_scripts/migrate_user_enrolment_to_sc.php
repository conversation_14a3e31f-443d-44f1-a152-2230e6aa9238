<?php

/*
 * This script should change the enrolment method of enrolled users in courses based on standalone course enrolment
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

//die();

global $DB;

$sql = "SELECT sc.id, sc.courseid cid FROM {tool_standalonecourses} sc
        JOIN {course} c ON c.id = sc.courseid";

$records = $DB->get_records_sql($sql);

$plugin_config = get_config('tool_standalonecourses');
$roleid = $plugin_config->role_sc_request;


$enrol_sc = enrol_get_plugin('standalone');

$enrol_manual = enrol_get_plugin('manual');

foreach ($records as $record) {

  $courseid = $record->cid;

  echo "courseid: $courseid\n";

//  $users = $DB->get_records('tool_sc_requests', ['courseid'=>$courseid,'approval_status'=>1],'','userid');


  $instances = enrol_get_instances($courseid, true);

  $found_sc_instance = false;

  foreach ($instances as $instance) {
    if ($instance->enrol=='standalone') {
      // An instance with the specified customint1 exists, so we do nothing
      $sc_instance = $instance;
      $found_sc_instance = true;
      break;
    }
  }
  // If no existing instance is found, add a new one with customint1 set to $lpid

  if (!$found_sc_instance) {
    $fields = ['customint1' => $record->id];
    $course = get_course($courseid);
    $instance_id = $enrol_sc->add_instance($course, $fields);

    $sc_instance = $DB->get_record('enrol',['id'=>$instance_id]);
  }

//  $manual_instance = $DB->get_record('enrol', ['enrol' => 'manual', 'courseid' => $courseid, 'status' => ENROL_INSTANCE_ENABLED]);

//  foreach($users as $user) {
//
//    $enrol_sc->enrol_user($sc_instance, $user->userid, $roleid, 0, 0);
//
//    if($manual_instance) {
//      $enrol_manual->unenrol_user($manual_instance, $user->userid);
//    }
//  }

}
