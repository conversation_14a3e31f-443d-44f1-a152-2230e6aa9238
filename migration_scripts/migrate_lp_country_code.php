<?php
/*
 * Note: This script should run after all the organisation/departments has been created.
 *
 * This script should update country_code in learning_plan_availability table.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';


global $DB;


$update_sql = "UPDATE {learning_plan_availability} lpa
JOIN {local_orgs_departments} dept ON lpa.country_code = dept.idnumber AND dept.is_country = 1
SET lpa.country_code = dept.id";

$sql = "SELECT lpa.id, dept.id AS dept_id FROM {learning_plan_availability} lpa
        JOIN {local_orgs_departments} dept ON lpa.country_code = dept.idnumber AND dept.is_country = 1";


try{
  $transaction = $DB->start_delegated_transaction();
  $recordset = $DB->get_recordset_sql($sql);

  foreach($recordset as $record) {
    $data = new stdClass();
    $data->id = $record->id;
    $data->country_code = $record->dept_id;

    $DB->update_record("learning_plan_availability", $data);
  }
  $transaction->allow_commit();
  echo "Everything is done!";
} catch (\Exception $e) {
  $transaction->rollback($e);
  echo "Something went wrong!";
}
