<?php
/*
 * Note: Run this script only once.
 * This script should insert timezone data in mdl_f2f_cf_timezone table.
 *
 * This script should only once.
 */
define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';

global $DB;
//$DB->set_debug(true);
if ($DB->record_exists('f2f_cf_timezone', ['timezone' => 'America/Detroit'])) {
//  echo 'Already done.';
//  die();
  $sql = "TRUNCATE TABLE `mdl_f2f_cf_timezone`";
  $DB->execute($sql);

  $sql = "TRUNCATE TABLE `mdl_f2f_cf_tz_country`";
  $DB->execute($sql);

  $sql = "TRUNCATE TABLE `mdl_f2f_cf_city`";
  $DB->execute($sql);

  $sql = "TRUNCATE TABLE `mdl_f2f_cf_location`";
  $DB->execute($sql);
}

$sql = "INSERT INTO `mdl_f2f_cf_timezone` (`timezone`, `timezonecode`) VALUES
('America/Detroit', 'ET'),
('America/Chicago', 'CT'),
('America/Boise', 'MT'),
('America/Los_Angeles', 'PT'),
('Pacific/Honolulu', 'HT'),
('Africa/Abidjan', NULL),
('Africa/Accra', NULL),
('Africa/Addis_Ababa', NULL),
('Africa/Algiers', NULL),
('Africa/Asmara', NULL),
('Africa/Bamako', NULL),
('Africa/Bangui', NULL),
('Africa/Banjul', NULL),
('Africa/Bissau', NULL),
('Africa/Blantyre', NULL),
('Africa/Brazzaville', NULL),
('Africa/Bujumbura', NULL),
('Africa/Cairo', NULL),
('Africa/Casablanca', NULL),
('Africa/Ceuta', NULL),
('Africa/Conakry', NULL),
('Africa/Dakar', NULL),
('Africa/Dar_es_Salaam', NULL),
('Africa/Djibouti', NULL),
('Africa/Douala', NULL),
('Africa/El_Aaiun', NULL),
('Africa/Freetown', NULL),
('Africa/Gaborone', NULL),
('Africa/Harare', NULL),
('Africa/Johannesburg', NULL),
('Africa/Juba', NULL),
('Africa/Kampala', NULL),
('Africa/Khartoum', NULL),
('Africa/Kigali', NULL),
('Africa/Kinshasa', NULL),
('Africa/Lagos', NULL),
('Africa/Libreville', NULL),
('Africa/Lome', NULL),
('Africa/Luanda', NULL),
('Africa/Lubumbashi', NULL),
('Africa/Lusaka', NULL),
('Africa/Malabo', NULL),
('Africa/Maputo', NULL),
('Africa/Maseru', NULL),
('Africa/Mbabane', NULL),
('Africa/Mogadishu', NULL),
('Africa/Monrovia', NULL),
('Africa/Nairobi', NULL),
('Africa/Ndjamena', NULL),
('Africa/Niamey', NULL),
('Africa/Nouakchott', NULL),
('Africa/Ouagadougou', NULL),
('Africa/Porto-Novo', NULL),
('Africa/Sao_Tome', NULL),
('Africa/Tripoli', NULL),
('Africa/Tunis', NULL),
('Africa/Windhoek', NULL),
('America/Adak', NULL),
('America/Anchorage', NULL),
('America/Anguilla', NULL),
('America/Antigua', NULL),
('America/Araguaina', NULL),
('America/Argentina/Buenos_Aires', NULL),
('America/Argentina/Catamarca', NULL),
('America/Argentina/Cordoba', NULL),
('America/Argentina/Jujuy', NULL),
('America/Argentina/La_Rioja', NULL),
('America/Argentina/Mendoza', NULL),
('America/Argentina/Rio_Gallegos', NULL),
('America/Argentina/Salta', NULL),
('America/Argentina/San_Juan', NULL),
('America/Argentina/San_Luis', NULL),
('America/Argentina/Tucuman', NULL),
('America/Argentina/Ushuaia', NULL),
('America/Aruba', NULL),
('America/Asuncion', NULL),
('America/Atikokan', NULL),
('America/Bahia', NULL),
('America/Bahia_Banderas', NULL),
('America/Barbados', NULL),
('America/Belem', NULL),
('America/Belize', NULL),
('America/Blanc-Sablon', NULL),
('America/Boa_Vista', NULL),
('America/Bogota', NULL),
('America/Cambridge_Bay', NULL),
('America/Campo_Grande', NULL),
('America/Cancun', NULL),
('America/Caracas', NULL),
('America/Cayenne', NULL),
('America/Cayman', NULL),
('America/Chihuahua', NULL),
('America/Costa_Rica', NULL),
('America/Creston', NULL),
('America/Cuiaba', NULL),
('America/Curacao', NULL),
('America/Danmarkshavn', NULL),
('America/Dawson', NULL),
('America/Dawson_Creek', NULL),
('America/Denver', NULL),
('America/Dominica', NULL),
('America/Edmonton', NULL),
('America/Eirunepe', NULL),
('America/El_Salvador', NULL),
('America/Fort_Nelson', NULL),
('America/Fortaleza', NULL),
('America/Glace_Bay', NULL),
('America/Goose_Bay', NULL),
('America/Grand_Turk', NULL),
('America/Grenada', NULL),
('America/Guadeloupe', NULL),
('America/Guatemala', NULL),
('America/Guayaquil', NULL),
('America/Guyana', NULL),
('America/Halifax', NULL),
('America/Havana', NULL),
('America/Hermosillo', NULL),
('America/Indiana/Indianapolis', NULL),
('America/Indiana/Knox', NULL),
('America/Indiana/Marengo', NULL),
('America/Indiana/Petersburg', NULL),
('America/Indiana/Tell_City', NULL),
('America/Indiana/Vevay', NULL),
('America/Indiana/Vincennes', NULL),
('America/Indiana/Winamac', NULL),
('America/Inuvik', NULL),
('America/Iqaluit', NULL),
('America/Jamaica', NULL),
('America/Juneau', NULL),
('America/Kentucky/Louisville', NULL),
('America/Kentucky/Monticello', NULL),
('America/Kralendijk', NULL),
('America/La_Paz', NULL),
('America/Lima', NULL),
('America/Lower_Princes', NULL),
('America/Maceio', NULL),
('America/Managua', NULL),
('America/Manaus', NULL),
('America/Marigot', NULL),
('America/Martinique', NULL),
('America/Matamoros', NULL),
('America/Mazatlan', NULL),
('America/Menominee', NULL),
('America/Merida', NULL),
('America/Metlakatla', NULL),
('America/Mexico_City', NULL),
('America/Miquelon', NULL),
('America/Moncton', NULL),
('America/Monterrey', NULL),
('America/Montevideo', NULL),
('America/Montserrat', NULL),
('America/Nassau', NULL),
('America/New_York', NULL),
('America/Nipigon', NULL),
('America/Nome', NULL),
('America/Noronha', NULL),
('America/North_Dakota/Beulah', NULL),
('America/North_Dakota/Center', NULL),
('America/North_Dakota/New_Salem', NULL),
('America/Nuuk', NULL),
('America/Ojinaga', NULL),
('America/Panama', NULL),
('America/Pangnirtung', NULL),
('America/Paramaribo', NULL),
('America/Phoenix', NULL),
('America/Port_of_Spain', NULL),
('America/Port-au-Prince', NULL),
('America/Porto_Velho', NULL),
('America/Puerto_Rico', NULL),
('America/Punta_Arenas', NULL),
('America/Rainy_River', NULL),
('America/Rankin_Inlet', NULL),
('America/Recife', NULL),
('America/Regina', NULL),
('America/Resolute', NULL),
('America/Rio_Branco', NULL),
('America/Santarem', NULL),
('America/Santiago', NULL),
('America/Santo_Domingo', NULL),
('America/Sao_Paulo', NULL),
('America/Scoresbysund', NULL),
('America/Sitka', NULL),
('America/St_Barthelemy', NULL),
('America/St_Johns', NULL),
('America/St_Kitts', NULL),
('America/St_Lucia', NULL),
('America/St_Thomas', NULL),
('America/St_Vincent', NULL),
('America/Swift_Current', NULL),
('America/Tegucigalpa', NULL),
('America/Thule', NULL),
('America/Thunder_Bay', NULL),
('America/Tijuana', NULL),
('America/Toronto', NULL),
('America/Tortola', NULL),
('America/Vancouver', NULL),
('America/Whitehorse', NULL),
('America/Winnipeg', NULL),
('America/Yakutat', NULL),
('America/Yellowknife', NULL),
('Antarctica/Casey', NULL),
('Antarctica/Davis', NULL),
('Antarctica/DumontDUrville', NULL),
('Antarctica/Macquarie', NULL),
('Antarctica/Mawson', NULL),
('Antarctica/McMurdo', NULL),
('Antarctica/Palmer', NULL),
('Antarctica/Rothera', NULL),
('Antarctica/Syowa', NULL),
('Antarctica/Troll', NULL),
('Antarctica/Vostok', NULL),
('Arctic/Longyearbyen', NULL),
('Asia/Aden', NULL),
('Asia/Almaty', NULL),
('Asia/Amman', NULL),
('Asia/Anadyr', NULL),
('Asia/Aqtau', NULL),
('Asia/Aqtobe', NULL),
('Asia/Ashgabat', NULL),
('Asia/Atyrau', NULL),
('Asia/Baghdad', NULL),
('Asia/Bahrain', NULL),
('Asia/Baku', NULL),
('Asia/Bangkok', NULL),
('Asia/Barnaul', NULL),
('Asia/Beirut', NULL),
('Asia/Bishkek', NULL),
('Asia/Brunei', NULL),
('Asia/Chita', NULL),
('Asia/Choibalsan', NULL),
('Asia/Colombo', NULL),
('Asia/Damascus', NULL),
('Asia/Dhaka', NULL),
('Asia/Dili', NULL),
('Asia/Dubai', NULL),
('Asia/Dushanbe', NULL),
('Asia/Famagusta', NULL),
('Asia/Gaza', NULL),
('Asia/Hebron', NULL),
('Asia/Ho_Chi_Minh', NULL),
('Asia/Hong_Kong', NULL),
('Asia/Hovd', NULL),
('Asia/Irkutsk', NULL),
('Asia/Jakarta', NULL),
('Asia/Jayapura', NULL),
('Asia/Jerusalem', NULL),
('Asia/Kabul', NULL),
('Asia/Kamchatka', NULL),
('Asia/Karachi', NULL),
('Asia/Kathmandu', NULL),
('Asia/Khandyga', NULL),
('Asia/Kolkata', NULL),
('Asia/Krasnoyarsk', NULL),
('Asia/Kuala_Lumpur', NULL),
('Asia/Kuching', NULL),
('Asia/Kuwait', NULL),
('Asia/Macau', NULL),
('Asia/Magadan', NULL),
('Asia/Makassar', NULL),
('Asia/Manila', NULL),
('Asia/Muscat', NULL),
('Asia/Nicosia', NULL),
('Asia/Novokuznetsk', NULL),
('Asia/Novosibirsk', NULL),
('Asia/Omsk', NULL),
('Asia/Oral', NULL),
('Asia/Phnom_Penh', NULL),
('Asia/Pontianak', NULL),
('Asia/Pyongyang', NULL),
('Asia/Qatar', NULL),
('Asia/Qostanay', NULL),
('Asia/Qyzylorda', NULL),
('Asia/Riyadh', NULL),
('Asia/Sakhalin', NULL),
('Asia/Samarkand', NULL),
('Asia/Seoul', NULL),
('Asia/Shanghai', NULL),
('Asia/Singapore', NULL),
('Asia/Srednekolymsk', NULL),
('Asia/Taipei', NULL),
('Asia/Tashkent', NULL),
('Asia/Tbilisi', NULL),
('Asia/Tehran', NULL),
('Asia/Thimphu', NULL),
('Asia/Tokyo', NULL),
('Asia/Tomsk', NULL),
('Asia/Ulaanbaatar', NULL),
('Asia/Urumqi', NULL),
('Asia/Ust-Nera', NULL),
('Asia/Vientiane', NULL),
('Asia/Vladivostok', NULL),
('Asia/Yakutsk', NULL),
('Asia/Yangon', NULL),
('Asia/Yekaterinburg', NULL),
('Asia/Yerevan', NULL),
('Atlantic/Azores', NULL),
('Atlantic/Bermuda', NULL),
('Atlantic/Canary', NULL),
('Atlantic/Cape_Verde', NULL),
('Atlantic/Faroe', NULL),
('Atlantic/Madeira', NULL),
('Atlantic/Reykjavik', NULL),
('Atlantic/South_Georgia', NULL),
('Atlantic/St_Helena', NULL),
('Atlantic/Stanley', NULL),
('Australia/Adelaide', NULL),
('Australia/Brisbane', NULL),
('Australia/Broken_Hill', NULL),
('Australia/Darwin', NULL),
('Australia/Eucla', NULL),
('Australia/Hobart', NULL),
('Australia/Lindeman', NULL),
('Australia/Lord_Howe', NULL),
('Australia/Melbourne', NULL),
('Australia/Perth', NULL),
('Australia/Sydney', NULL),
('Europe/Amsterdam', NULL),
('Europe/Andorra', NULL),
('Europe/Astrakhan', NULL),
('Europe/Athens', NULL),
('Europe/Belgrade', NULL),
('Europe/Berlin', NULL),
('Europe/Bratislava', NULL),
('Europe/Brussels', NULL),
('Europe/Bucharest', NULL),
('Europe/Budapest', NULL),
('Europe/Busingen', NULL),
('Europe/Chisinau', NULL),
('Europe/Copenhagen', NULL),
('Europe/Dublin', NULL),
('Europe/Gibraltar', NULL),
('Europe/Guernsey', NULL),
('Europe/Helsinki', NULL),
('Europe/Isle_of_Man', NULL),
('Europe/Istanbul', NULL),
('Europe/Jersey', NULL),
('Europe/Kaliningrad', NULL),
('Europe/Kiev', NULL),
('Europe/Kirov', NULL),
('Europe/Lisbon', NULL),
('Europe/Ljubljana', NULL),
('Europe/London', NULL),
('Europe/Luxembourg', NULL),
('Europe/Madrid', NULL),
('Europe/Malta', NULL),
('Europe/Mariehamn', NULL),
('Europe/Minsk', NULL),
('Europe/Monaco', NULL),
('Europe/Moscow', NULL),
('Europe/Oslo', NULL),
('Europe/Paris', NULL),
('Europe/Podgorica', NULL),
('Europe/Prague', NULL),
('Europe/Riga', NULL),
('Europe/Rome', NULL),
('Europe/Samara', NULL),
('Europe/San_Marino', NULL),
('Europe/Sarajevo', NULL),
('Europe/Saratov', NULL),
('Europe/Simferopol', NULL),
('Europe/Skopje', NULL),
('Europe/Sofia', NULL),
('Europe/Stockholm', NULL),
('Europe/Tallinn', NULL),
('Europe/Tirane', NULL),
('Europe/Ulyanovsk', NULL),
('Europe/Uzhgorod', NULL),
('Europe/Vaduz', NULL),
('Europe/Vatican', NULL),
('Europe/Vienna', NULL),
('Europe/Vilnius', NULL),
('Europe/Volgograd', NULL),
('Europe/Warsaw', NULL),
('Europe/Zagreb', NULL),
('Europe/Zaporozhye', NULL),
('Europe/Zurich', NULL),
('Indian/Antananarivo', NULL),
('Indian/Chagos', NULL),
('Indian/Christmas', NULL),
('Indian/Cocos', NULL),
('Indian/Comoro', NULL),
('Indian/Kerguelen', NULL),
('Indian/Mahe', NULL),
('Indian/Maldives', NULL),
('Indian/Mauritius', NULL),
('Indian/Mayotte', NULL),
('Indian/Reunion', NULL),
('Pacific/Apia', NULL),
('Pacific/Auckland', NULL),
('Pacific/Bougainville', NULL),
('Pacific/Chatham', NULL),
('Pacific/Chuuk', NULL),
('Pacific/Easter', NULL),
('Pacific/Efate', NULL),
('Pacific/Fakaofo', NULL),
('Pacific/Fiji', NULL),
('Pacific/Funafuti', NULL),
('Pacific/Galapagos', NULL),
('Pacific/Gambier', NULL),
('Pacific/Guadalcanal', NULL),
('Pacific/Guam', NULL),
('Pacific/Kanton', NULL),
('Pacific/Kiritimati', NULL),
('Pacific/Kosrae', NULL),
('Pacific/Kwajalein', NULL),
('Pacific/Majuro', NULL),
('Pacific/Marquesas', NULL),
('Pacific/Midway', NULL),
('Pacific/Nauru', NULL),
('Pacific/Niue', NULL),
('Pacific/Norfolk', NULL),
('Pacific/Noumea', NULL),
('Pacific/Pago_Pago', NULL),
('Pacific/Palau', NULL),
('Pacific/Pitcairn', NULL),
('Pacific/Pohnpei', NULL),
('Pacific/Port_Moresby', NULL),
('Pacific/Rarotonga', NULL),
('Pacific/Saipan', NULL),
('Pacific/Tahiti', NULL),
('Pacific/Tarawa', NULL),
('Pacific/Tongatapu', NULL),
('Pacific/Wake', NULL),
('Pacific/Wallis', NULL),
('Central Standard', NULL),
('Atlantic Standard', NULL),
('UTC', NULL)";


$DB->execute($sql);

$tz_country = [
  "America/Detroit" =>	"United States",
  "America/Chicago" =>	"United States",
  "America/Boise" =>	"United States",
  "Pacific/Honolulu" =>	"United States",
  "America/Toronto" =>	"Canada",
  "America/Los_Angeles" =>	"United States",
  "America/Edmonton" =>	"Canada",
  "America/Halifax" =>	"Canada",
  "America/St_Johns" =>	"Canada",
  "America/Vancouver" =>	"Canada",
  "America/Winnipeg" =>	"Canada",
  "Atlantic Standard" =>	"United States",
  "Central Standard" =>	"Mexico",
  "Europe/Amsterdam" =>	"Netherlands",
  "Europe/Rome" =>	"Italy",
  ];

$insert_sql = "INSERT INTO `mdl_f2f_cf_tz_country` (`country`, `country_code`, `department_id`, `timezone`, `usermodified`, `timecreated`, `timemodified`) VALUES ";
$values_sql = [];
$time = time();
Foreach ($tz_country as $tz => $country) {
  $dept = $DB->get_record('local_orgs_departments',["fullname"=>$country]);
  if ($dept == false) {
    $dept = new stdClass();
    $dept->id = 0;
    $dept->idnumber = "ZZ";
  }
  $values_sql[] = "('{$country}', '{$dept->idnumber}', {$dept->id}, '{$tz}', 2, {$time}, {$time})";
}

$values_sql_str = implode(',', $values_sql);
$final_sql = $insert_sql . $values_sql_str;
$DB->execute($final_sql);

$cities = [
  "Calgary, Canada" => "CA",
  "Concord, Canada" => "CA",
  "Coquitlam, Canada" => "CA",
  "Delta, Canada" => "CA",
  "Montreal, Canada" => "CA",
  "Toronto, Canada" => "CA",
  "Toronto, ON" => "CA",
  "Victoria, Canada" => "CA",
  "Sona VR, Italy" => "IT",
  "Querétaro, Mexico" => "MX",
  "Albany, NY" => "US",
  "Anaheim, CA" => "US",
  "Ann Arbor, MI" => "US",
  "Arlington, TX" => "US",
  "Austin, TX" => "US",
  "Bakersfield, CA" => "US",
  "Baltimore, MD" => "US",
  "Berkeley, MO" => "US",
  "Burlington, VT" => "US",
  "Caguas, PR" => "US",
  "Canton, MI" => "US",
  "Charleston, SC" => "US",
  "Charlotte, NV" => "US",
  "Charlottesville, VA" => "US",
  "Chesapeake, VA" => "US",
  "Chico, CA" => "US",
  "Clovis, CA" => "US",
  "Columbia, MD" => "US",
  "Columbus, OH" => "US",
  "Corona, CA" => "US",
  "Dallas, TX" => "US",
  "Denver, CO" => "US",
  "Detroit, MI" => "US",
  "Edmonton, NW" => "US",
  "El Cajon, CA" => "US",
  "El Paso, TX" => "US",
  "Fayetteville, AR" => "US",
  "Fort Collins, CO" => "US",
  "Fremont, CA" => "US",
  "Fresno, CA" => "US",
  "Fullerton, CA" => "US",
  "Granozzo con Monticello, NO" => "US",
  "Guaynabo, PR" => "US",
  "Honolulu, HI" => "US",
  "Houston, TX" => "US",
  "Irving, TX" => "US",
  "Kansas City, KS" => "US",
  "Kansas City, MO" => "US",
  "Kent, WA" => "US",
  "Las Vegas" => "US",
  "Little Rock, AR" => "US",
  "Los Angeles, CA" => "US",
  "Louisville, KY" => "US",
  "Manteca, CA" => "US",
  "Meridian, ID" => "US",
  "Mesa, AZ" => "US",
  "Miami, FL" => "US",
  "Minneapolis, MN" => "US",
  "New Orleans, LA" => "US",
  "New York, NY" => "US",
  "North Hollywood, CA" => "US",
  "Oklahoma City, OK" => "US",
  "Orem, OT" => "US",
  "Orlando, FL" => "US",
  "Palmetto, FL" => "US",
  "Pensacola, FL" => "US",
  "Phoenix, AZ" => "US",
  "Pittsburgh, PA" => "US",
  "Pompano Beach, FL" => "US",
  "Portland, ME" => "US",
  "Raleigh, NC" => "US",
  "Richmond, VA" => "US",
  "Riverside, CA" => "US",
  "Rogers, AR" => "US",
  "Sacramento, CA" => "US",
  "San Antonio, TX" => "US",
  "San Diego, CA" => "US",
  "Santa Rosa, CA" => "US",
  "Springfield, IL" => "US",
  "St. Paul, MN" => "US",
  "State College, PA" => "US",
  "Tucson, AZ" => "US",
  "Virginia Beach, VA" => "US",
  "Webinar" => "US",
  "West Columbia, SC" => "US",
  "West Valley City, UT" => "US",
  "Westminster, CA" => "US",
  "Worcester" => "US",
  ];

$insert_sql = "INSERT INTO `mdl_f2f_cf_city` (`city`, `country_code`, `usermodified`, `timecreated`, `timemodified`) VALUES ";
$values_sql = [];
$time = time();
Foreach ($cities as $city => $country_code) {
  $values_sql[] = "('{$city}', '{$country_code}', 2, {$time}, {$time})";
}

$values_sql_str = implode(',', $values_sql);
$final_sql = $insert_sql . $values_sql_str;
$DB->execute($final_sql);

$locations = [
  "Wesco - 9400 N Royal Ln #100, Irving, TX 75063" => "Irving, TX",
  "Rexel - 6901 W Tidwell Rd, Houston, TX 77092" => "Houston, TX",
  "Greentech Renewables - 7527 Exchange Dr, Orlando, FL 32809" => "Orlando, FL",
  "CFM Equipment Sacramento - 1644 Main Ave, Suite #1, Sacramento, CA 95838" => "Sacramento, CA",
  "Empower Home Solar - 4475 N Bendel Ave #101 Fresno, CA, 93722" => "Fresno, CA",
  "Semper El Cajon - 1805 John Towers Ave, El Cajon, CA 92020" => "El Cajon, CA",
  "Semper Manteca - 3025 E Palm Ave, Building C Suite 101, Manteca, CA 95337" => "Manteca, CA",
  "Semper Fresno - 5234 E. Pine Ave, Fresno, CA 93727" => "Fresno, CA",
  "LA Solar Group - 16238 Raymer Avenue, Suite B, Los Angeles, CA 91406" => "Los Angeles, CA",
  "Wesco Fresno - 3712 W. Gettysburg Ave, Fresno, CA 93722" => "Fresno, CA",
  "Greentech Renewables - 5003 Eisenhauer Rd, San Antonio, TX 78218" => "San Antonio, TX",
  "Imagine Solar - 4000 Caven Road, Austin, TX 78744" => "Austin, TX",
  "5804 River Oaks Rd S, New Orleans, LA 70123" => "New Orleans, LA",
  "Enphase Fremont Office - 47341 Bayside Pkwy. Fremont, CA 94538" => "Fremont, CA",
  "Enphase Boise Office - 1819 S. Cobalt Point Way, Meridian, ID 83642" => "Meridian, ID",
  "Rexel - 1321 S State College Blvd, Fullerton CA 92831" => "Fullerton, CA",
  "Greentech Renewables- 115 Kentucky Street, Bakersfield, CA 93305" => "Bakersfield, CA",
  "Greentech Renewables - 9190 Activity Rd, San Diego, CA 92126" => "San Diego, CA",
  "CED Greentech - 1601 Iowa Ave, Riverside, CA 92507" => "Riverside, CA",
  "STL - 8921 Frost Ave, Berkeley MO 63134" => "Berkeley, MO",
  "Inter-Island Solar Supply - 761 Ahua St, Honolulu, HI 96819" => "Honolulu, HI",
  "CED - 1846 N Topping Ave, Kansas City, MO 64120" => "Kansas City, MO",
  "761 Ahua St., Honolulu, Hi 96819" => "Honolulu, HI",
  "CED Greentech Columbia South Carolina - 2500 Leaphart Rd, West Columbia, SC 29169" => "West Columbia, SC",
  "CED Anaheim - 2861 E La Palma Ave, Anaheim CA 92806" => "Anaheim, CA",
  "Greentech Renewables- 1601 Iowa Ave, Riverside, CA 92507" => "Riverside, CA",
  "CED Greentech - 9475 Gerwig Lane, Suite F, Columbia, MD 21046" => "Columbia, MD",
  "CED Greentech - 2930 Yonkers Rd, Raleigh, NC 27604" => "Raleigh, NC",
  "CED Santa Rosa - 3490 Regional Parkway, Santa Rosa, CA 95403" => "Santa Rosa, CA",
  "Private residence - 4340 N Mainard Drive, Tucson, AZ 85719" => "Tucson, AZ",
  "CED Greentech Phoenix - 4535 E Elwood St Ste 105, Phoenix, AZ 85040" => "Phoenix, AZ",
  "1825 N Walnut Ave, Oklahoma City, OK 73105" => "Oklahoma City, OK",
  "801 SE 59th Street Oklahoma City, OK 73129" => "Oklahoma City, OK",
  "3139 N Andrews Ave Ext, Pompano Beach, FL 33064" => "Pompano Beach, FL",
  "7527 Exchange Dr, Orlando, FL 32809" => "Orlando, FL",
  "CFM Equipment Sacramento - 1644 Main Ave, Suite #1, Sacramento, CA 95838" => "Sacramento, CA",
  "Empower Home Solar - 4475 N Bendel Ave #101, Fresno, CA, 93722" => "Fresno, CA",
  "Semper El Cajon - 1805 John Towers Ave, El Cajon, CA 92020" => "El Cajon, CA",
  "Semper Manteca - 3025 E Palm Ave, Building C Suite 101, Manteca, CA 95337" => "Manteca, CA",
  "Semper Fresno - 5234 E. Pine Ave, Fresno, CA 93727" => "Fresno, CA",
  "LA Solar Group - 16238 Raymer Avenue, Suite B, Los Angeles, CA 91406" => "Los Angeles, CA",
  "Wesco Fresno - 3712 W. Gettysburg Ave, Fresno, CA 93722" => "Fresno, CA",
  "McNaughton-McKay - 2255 Citygate Dr, Columbus, OH 43219" => "Columbus, OH",
  "CED Greentech Detroit - 41106 Koppernick Rd, Canton MI 48487" => "Canton, MI",
  "Codale Electric Supply - 3920 W Sunset Rd, Las Vegas, NV 89118" => "Las Vegas",
  "Renu Energy - 1515 S. Clarkson St. Charlotte NC 28208" => "Charlotte, NV",
  "Envinity - 25 Decibel Rd. suite 205, State Collage PA 16801" => "State College, PA",
  "CED Greentech - 2242 S. Presidents Drive, Suite A & B, West Valley City, UT 84120" => "West Valley City, UT",
  "Codale Electric Supply - 362 Commerce Loop, Orem, UT 84058" => "Orem, OT",
  "CED Greentech - 5400 Main St NE, Suite 113, Minneapolis, MN 55421" => "Minneapolis, MN",
  "Greentech Renewables - 2475 West 2nd Ave, Denver, CO 80223" => "Denver, CO",
  "CED Greentech - 22436 72nd Ave. S. Kent, WA 98032" => "Kent, WA",
  "Platt - 20021 59th Place South, Kent WA, 98032" => "Kent, WA",
  "1264 Energy Ln, St Paul, MN, United States" => "St. Paul, MN",
  "1136 Adams St, Kansas City, KS 66103" => "Kansas City, KS",
  "14800 Santa Fe Crossings Dr Edmond, OK 73013" => "Oklahoma City, OK",
  "5214 W Village Pkwy #100 Rogers, AR 72758" => "Fayetteville, AR",
  "CED Greentech (New location) - 1742 W. Atlantic Blvd. Pompano Beach FL 33069" => "Miami, FL",
  "CED Greentech - 400-3 Sharkey Drive, Maumelle, AR 72116" => "Little Rock, AR",
  "CED Greentech warehouse- 1070 Marauder Street, Suite 110, Chico, CA 95973" => "Chico, CA",
  "Greentech Renewables – 102 Karner Road, Albany, NY 12205" => "Albany, NY",
  "Turnkey Energy - 3954 M Ann Ave, Fresno, CA 92727" => "Fresno, CA",
  "Charge Solar Warehouse - 120 Ellis Dr, Unit -8, Barrie, ON L4N 9B2" => "Montreal, Canada",
  "CED Greentech - 11501 Rojas, Suite H, El Paso TX, 79936" => "El Paso, TX",
  "CED New Warehouse - 322 Leroy Rd, Williston, VT 05495" => "Burlington, VT",
  "CED Greentech - 33 Spring Hill Rd, Saco, ME 04072" => "Portland, ME",
  "SunWatt Energy - 3023 Durazno Ave, El Paso, TX 79905" => "El Paso, TX",
  "BayWa - 4030 E Quenton Dr., Mesa, AZ 85215" => "Mesa, AZ",
  "BayWa RTC" => "Worcester",
  "Capital Electric - 600 W. Hamburg St, Baltimore, MD 21230" => "Baltimore, MD",
  "BayWa r.e. - 2151 Blount Rd, Pompano Beach, FL 33069" => "Pompano Beach, FL",
  "Greentech Renewables - 3145 S. Northpointe Drive Suite 103, Fresno CA 93725" => "Fresno, CA",
  "Infinity Energy, 5096 N Blythe Ave Ste 100, Fresno, CA, 93722" => "Fresno, CA",
  "Capital Electric - 5894 Thurston Ave, Virginia Beach, VA 23455" => "Virginia Beach, VA",
  "Capital Electric - 1122 Executive Boulevard, Suite B, Chesapeake, VA 23320" => "Chesapeake, VA",
  "Capital Electric - 2100 Berkmar Drive, Charlottesville, VA 22901" => "Charlottesville, VA",
  "Greentech Renewables - 912 113th Street, Arlington TX 76011" => "Arlington, TX",
  "Infinity Energy - 7930 Breen Dr, Houston, TX, 77064, US" => "Houston, TX",
  "Greentech Renewables - 3361 Copter Rd., Pensacola, FL 32514" => "Pensacola, FL",
  "NC Solar Now - 2509 Atlantic Avenue, Raleigh, NC 27604" => "Raleigh, NC",
  "Warren del Caribe - PR-1 km 29.4, Caguas, PR 00725" => "Caguas, PR",
  "Carr. Estatal 420 kilómetro 5.6, El Rosario, Querétaro, México" => "Querétaro, Mexico",
  "Customized Training Location" => "Fremont, CA",
  "7100 Fair Ave, North Hollywood, CA 91605" => "North Hollywood, CA",
  "Inland Empire - 370 Meyer Circle, Corona, CA 92879" => "Corona, CA",
  "Orange County - 7200 Fenwick Ln., Westminster, CA 92683" => "Westminster, CA",
  "Capital Electric, 4801 A Rivers Ave. Charleston SC 29406" => "Charleston, SC",
  "933 Clint Moore Rd. Boca Raton, Florida 33487" => "Pompano Beach, FL",
  "City Electric Supply - 2201 Magnolia St, Richmond, VA 23223" => "Richmond, VA",
  "CED Greentech 4273 Carolina Ave Richmond, VA 23222" => "Richmond, VA",
  "870 CORDUROY RD, SUITE 100, LEWIS CENTER, OH 43035" => "Columbus, OH",
  "Koppernick Rd. Canton, MI 48187" => "Detroit, MI",
  "4300 Varsity Drive. Ann Arbor, MI 48108" => "Ann Arbor, MI",
  "6740 Theall Houston, TX" => "Houston, TX",
  "683 N Pollasky Ave" => "Clovis, CA",
  "165 Sun Pac Blvd – Unit 3, Brampton, ON" => "Toronto, ON",
  "City View Plaza, Tower 1, Suite 640, 48 PR-165, Guaynabo, PR 00966" => "Guaynabo, PR",
  "1-65 Martin Ross Ave. Toronto ON M3J 2L6" => "Toronto, Canada",
  "273B Bowes Rd, Unit 1 and 2, Concord ON L4K 1H8" => "Concord, Canada",
  "2511 51st Ave E Suite: 100, Palmetto, FL 34221" => "Palmetto, FL",
  "Circle L Solar - 5450 Stratum Dr, Fort Worth, TX 76137" => "Dallas, TX",
  "TriSmart Solar - 5064 Brush Creek Road, Fort Worth, Tx. 76119" => "Dallas, TX",
  "Enphase IQ Battery 5P" => "Webinar",
  "420 37th Ave NE Minneapolis, MN 55421" => "Minneapolis, MN",
  "400 Sharkey Dr #3, Maumelle, AR 72113" => "Little Rock, AR",
  "2201 N 17th St, Rogers, AR 72756" => "Rogers, AR",
  "3330 Tennyson Avenue, Victoria, BC, V8Z 3P3" => "Victoria, Canada",
  "1020 Derwent Way, V3M5R1 Delta, BC" => "Delta, Canada",
  "2230 Hartley Ave, Coquitlam, BC V3K 6X3" => "Coquitlam, Canada",
  "4703-101 Street NW, T6E 5C6 Edmonton" => "Edmonton, NW",
  "1440 Aviation Park NE Unit 119, Calgary, AB, T2E 7E2" => "Calgary, Canada",
  "7350 110 Ave SE #160, Calgary, AB T2C 3B8" => "Calgary, Canada",
  "7703 30 St SE, T2C 1V4 Calgary" => "Calgary, Canada",
  "45 Osage Ave Ste. A, Kansas City, KS 66105" => "Kansas City, KS",
  "700 N 9th St, Springfield, IL 62702" => "Springfield, IL",
  "9519 Civic Way Suite 100 Prospect, KY 40059" => "Louisville, KY",
  "340 S Campus Dr, Imperial, PA 15126" => "Pittsburgh, PA",
  "Località Casa Fasani, 137060 Sona VR, Italy" => "Sona VR, Italy",
  "80 13th Ave # 4, Ronkonkoma, NY 11779" => "New York, NY",
  "Via Dante Graziosi, 1 - 28060 Granozzo con Monticello NO" => "Granozzo con Monticello, NO",
  "CED Fort Collins - 1513 Webster Ct Suite E,F,G - Fort Collins, CO 80524" => "Fort Collins, CO",
  ];

$insert_sql = "INSERT INTO `mdl_f2f_cf_location` (`location`, `city_id`, `usermodified`, `timecreated`, `timemodified`) VALUES ";
$values_sql = [];
$time = time();
Foreach ($locations as $location => $city) {
  $dept = $DB->get_record('f2f_cf_city',["city"=>$city]);
  if ($dept == false) {
    $dept = new stdClass();
    $dept->id = 0;
  }
  $values_sql[] = "('{$location}', {$dept->id}, 2, {$time}, {$time})";
}

$values_sql_str = implode(',', $values_sql);
$final_sql = $insert_sql . $values_sql_str;
$DB->execute($final_sql);

echo 'Task completed successfully.' . PHP_EOL;
