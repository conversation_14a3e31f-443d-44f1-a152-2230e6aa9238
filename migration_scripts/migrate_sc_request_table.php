<?php
/*
 * Run this script only once
 * This script is rename mdl_standalone_course_requests to mdl_tool_sc_requests.
 *
 */

define('CLI_SCRIPT', true);

require_once __DIR__ . '/../config.php';


die();

global $DB;

$dbman = $DB->get_manager();

$table1 = new xmldb_table('tool_sc_requests');
$table2 = new xmldb_table('standalone_course_requests');

if ($dbman->table_exists($table2)) {

  if($dbman->table_exists($table1)) {
    $dbman->drop_table($table1);
  }
  $dbman->rename_table($table2, 'tool_sc_requests');
  echo 'Task completed successfully.' . PHP_EOL;
} else {
  echo 'Already done.' . PHP_EOL;
}
