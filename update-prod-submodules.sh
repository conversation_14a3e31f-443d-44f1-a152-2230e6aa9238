#!/bin/bash

echo "Starting production environment update..."

# Step 1: Initialize and fetch submodules
git submodule update --init

# Step 2: Set each submodule to track the production branch
git submodule foreach '
    echo "=== Processing $(pwd) ==="
    git fetch origin

    # Check if production branch exists
    if git show-ref --verify --quiet refs/heads/production; then
        echo "Checking out existing production branch"
        git checkout production
        git pull origin production
    elif git ls-remote --heads origin production | grep -q production; then
        echo "Creating local production branch from remote"
        git checkout -b production origin/production
    else
        echo "Creating new production branch from development"
        git checkout development
        git pull origin development
        git checkout -b production
        git push -u origin production
    fi
    echo "----------------------------------------"
'

echo "Production environment update completed!"
