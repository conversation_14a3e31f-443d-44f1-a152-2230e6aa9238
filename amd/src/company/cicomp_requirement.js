import Notification from 'core/notification';
import {addRequirementModal} from 'local_iauthorise/local/repository/modals';
import {prefetchStrings} from 'core/prefetch';
import {get_string as getString} from 'core/str';
import * as tableSelectors from 'core_table/local/dynamic/selectors';
import SELECTORS from 'local_iauthorise/local/selectors';
import Pending from 'core/pending';
import {refreshTableContent} from 'core_table/dynamic';


let cicompreqamdinitialised = false;
export const init = (systemContextId) => {

    if (cicompreqamdinitialised) {
        return;
    }

    prefetchStrings('local_iauthorise', ['add_requirement']);


    document.addEventListener('click', e => {

        if (!e || !e.target || (typeof e.target.closest === "undefined")) {
            return;
        }

        const addCicompReqElement = e.target.closest(SELECTORS.actions.cicompReqAdd);

        if (addCicompReqElement) {
            e.preventDefault();
            initialiseCicompReqCreation(e);
        }
    });

    cicompreqamdinitialised = true;
};

const initialiseCicompReqCreation = (event) => {

    let modalHeading = getString('add_requirement', 'local_iauthorise');

    const addCicompReqElement = event.target.closest(SELECTORS.actions.cicompReqAdd);
    const cicompId = addCicompReqElement.dataset.cicompId;

    const cicompReqModal = addRequirementModal(event.target, modalHeading, cicompId);

    const formSubmittedListener = function () {
        // Remove the event listener to prevent duplication
        cicompReqModal.modal.getRoot().off(cicompReqModal.events.FORM_SUBMITTED, formSubmittedListener);

        window.location.reload();
    };

    cicompReqModal.addEventListener(cicompReqModal.events.FORM_SUBMITTED, formSubmittedListener);
    cicompReqModal.show().then(() => {
        // Add icon to modal container
        cicompReqModal.modal.getRoot().find('.modal-dialog').addClass('modal-dialog-centered modal-xl');
    });
};