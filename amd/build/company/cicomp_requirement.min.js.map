{"version": 3, "file": "cicomp_requirement.min.js", "sources": ["../../src/company/cicomp_requirement.js"], "sourcesContent": ["import Notification from 'core/notification';\nimport {addRequirementModal} from 'local_iauthorise/local/repository/modals';\nimport {prefetchStrings} from 'core/prefetch';\nimport {get_string as getString} from 'core/str';\nimport * as tableSelectors from 'core_table/local/dynamic/selectors';\nimport SELECTORS from 'local_iauthorise/local/selectors';\nimport Pending from 'core/pending';\nimport {refreshTableContent} from 'core_table/dynamic';\n\n\nlet cicompreqamdinitialised = false;\nexport const init = (systemContextId) => {\n\n    if (cicompreqamdinitialised) {\n        return;\n    }\n\n    prefetchStrings('local_iauthorise', ['add_requirement']);\n\n\n    document.addEventListener('click', e => {\n\n        if (!e || !e.target || (typeof e.target.closest === \"undefined\")) {\n            return;\n        }\n\n        const addCicompReqElement = e.target.closest(SELECTORS.actions.cicompReqAdd);\n\n        if (addCicompReqElement) {\n            e.preventDefault();\n            initialiseCicompReqCreation(e);\n        }\n    });\n\n    cicompreqamdinitialised = true;\n};\n\nconst initialiseCicompReqCreation = (event) => {\n\n    let modalHeading = getString('add_requirement', 'local_iauthorise');\n\n    const addCicompReqElement = event.target.closest(SELECTORS.actions.cicompReqAdd);\n    const cicompId = addCicompReqElement.dataset.cicompId;\n\n    const cicompReqModal = addRequirementModal(event.target, modalHeading, cicompId);\n\n    const formSubmittedListener = function () {\n        // Remove the event listener to prevent duplication\n        cicompReqModal.modal.getRoot().off(cicompReqModal.events.FORM_SUBMITTED, formSubmittedListener);\n\n        window.location.reload();\n    };\n\n    cicompReqModal.addEventListener(cicompReqModal.events.FORM_SUBMITTED, formSubmittedListener);\n    cicompReqModal.show().then(() => {\n        // Add icon to modal container\n        cicompReqModal.modal.getRoot().find('.modal-dialog').addClass('modal-dialog-centered modal-xl');\n    });\n};"], "names": ["_getRequireWildcardCache", "e", "WeakMap", "r", "t", "_interopRequireDefault", "__esModule", "default", "_notification", "tableSelectors", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_interopRequireWildcard", "_selectors2", "_pending", "cicompreqamdinitialised", "_exports", "init", "systemContextId", "prefetchStrings", "document", "addEventListener", "target", "closest", "SELECTORS", "actions", "cicompReqAdd", "preventDefault", "initialiseCicompReqCreation", "event", "modalHeading", "getString", "cicompId", "dataset", "cicompReqModal", "addRequirementModal", "formSubmittedListener", "modal", "getRoot", "off", "events", "FORM_SUBMITTED", "window", "location", "reload", "show", "then", "find", "addClass"], "mappings": "6WAMmC,SAAAA,yBAAAC,GAAA,GAAA,mBAAAC,QAAA,OAAA,KAAA,IAAAC,EAAAD,IAAAA,QAAAE,EAAAF,IAAAA,eAAAF,yBAAA,SAAAC,GAAAA,OAAAA,EAAAG,EAAAD,IAAAF,EAAA,CAAA,SAAAI,uBAAAJ,GAAAA,OAAAA,GAAAA,EAAAK,WAAAL,EAAAM,CAAAA,QAAAN,EAAA,8EANnCO,cAAAH,uBAAAG,eAIAC,eAEmC,SAAAR,EAAAE,GAAAA,IAAAA,GAAAF,GAAAA,EAAAK,WAAAL,OAAAA,EAAAA,GAAAA,OAAAA,GAAAA,iBAAAA,GAAAA,mBAAAA,EAAAM,MAAAA,CAAAA,QAAAN,GAAAG,IAAAA,EAAAJ,yBAAAG,GAAA,GAAAC,GAAAA,EAAAM,IAAAT,GAAA,OAAAG,EAAAO,IAAAV,GAAA,IAAAW,EAAA,CAAAC,UAAA,MAAAC,EAAAC,OAAAC,gBAAAD,OAAAE,yBAAA,IAAA,IAAAC,KAAAjB,EAAAiB,GAAAA,YAAAA,GAAAC,CAAAA,EAAAA,eAAAC,KAAAnB,EAAAiB,GAAAG,CAAAA,IAAAA,EAAAP,EAAAC,OAAAE,yBAAAhB,EAAAiB,GAAAG,KAAAA,IAAAA,EAAAV,KAAAU,EAAAC,KAAAP,OAAAC,eAAAJ,EAAAM,EAAAG,GAAAT,EAAAM,GAAAjB,EAAAiB,GAAAN,OAAAA,EAAAL,QAAAN,EAAAG,GAAAA,EAAAkB,IAAArB,EAAAW,GAAAA,CAAA,CAFnCW,CAAAd,gBACAe,YAAAnB,uBAAAmB,aACAC,SAAApB,uBAAAoB,UAIA,IAAIC,yBAA0B,EAyB5BC,SAAAC,KAxBmBC,kBAEbH,2BAIJ,EAAAI,2BAAgB,mBAAoB,CAAC,oBAGrCC,SAASC,iBAAiB,SAAS/B,IAE/B,IAAKA,IAAMA,EAAEgC,aAAuC,IAArBhC,EAAEgC,OAAOC,QACpC,OAGwBjC,EAAEgC,OAAOC,QAAQC,YAAS5B,QAAC6B,QAAQC,gBAG3DpC,EAAEqC,iBACFC,4BAA4BtC,GAChC,IAGJyB,yBAA0B,EAAI,EAGlC,MAAMa,4BAA+BC,QAEjC,IAAIC,cAAe,EAAAC,KAAAA,YAAU,kBAAmB,oBAEhD,MACMC,SADsBH,MAAMP,OAAOC,QAAQC,YAAS5B,QAAC6B,QAAQC,cAC9BO,QAAQD,SAEvCE,gBAAiB,EAAAC,QAAAA,qBAAoBN,MAAMP,OAAQQ,aAAcE,UAEjEI,sBAAwB,WAE1BF,eAAeG,MAAMC,UAAUC,IAAIL,eAAeM,OAAOC,eAAgBL,uBAEzEM,OAAOC,SAASC,UAGpBV,eAAeb,iBAAiBa,eAAeM,OAAOC,eAAgBL,uBACtEF,eAAeW,OAAOC,MAAK,KAEvBZ,eAAeG,MAAMC,UAAUS,KAAK,iBAAiBC,SAAS,iCAAiC,GACjG,CACJ"}