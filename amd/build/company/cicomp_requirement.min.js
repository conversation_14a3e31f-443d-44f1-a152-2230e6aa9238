define("local_iauthorise/company/cicomp_requirement",["exports","core/notification","local_iauthorise/local/repository/modals","core/prefetch","core/str","core_table/local/dynamic/selectors","local_iauthorise/local/selectors","core/pending","core_table/dynamic"],(function(_exports,_notification,_modals,_prefetch,_str,tableSelectors,_selectors2,_pending,_dynamic){function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(_getRequireWildcardCache=function(e){return e?t:r})(e)}function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=_interopRequireDefault(_notification),tableSelectors=function(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,t&&t.set(e,n),n}(tableSelectors),_selectors2=_interopRequireDefault(_selectors2),_pending=_interopRequireDefault(_pending);let cicompreqamdinitialised=!1;_exports.init=systemContextId=>{cicompreqamdinitialised||((0,_prefetch.prefetchStrings)("local_iauthorise",["add_requirement"]),document.addEventListener("click",(e=>{if(!e||!e.target||void 0===e.target.closest)return;e.target.closest(_selectors2.default.actions.cicompReqAdd)&&(e.preventDefault(),initialiseCicompReqCreation(e))})),cicompreqamdinitialised=!0)};const initialiseCicompReqCreation=event=>{let modalHeading=(0,_str.get_string)("add_requirement","local_iauthorise");const cicompId=event.target.closest(_selectors2.default.actions.cicompReqAdd).dataset.cicompId,cicompReqModal=(0,_modals.addRequirementModal)(event.target,modalHeading,cicompId),formSubmittedListener=function(){cicompReqModal.modal.getRoot().off(cicompReqModal.events.FORM_SUBMITTED,formSubmittedListener),window.location.reload()};cicompReqModal.addEventListener(cicompReqModal.events.FORM_SUBMITTED,formSubmittedListener),cicompReqModal.show().then((()=>{cicompReqModal.modal.getRoot().find(".modal-dialog").addClass("modal-dialog-centered modal-xl")}))}}));

//# sourceMappingURL=cicomp_requirement.min.js.map