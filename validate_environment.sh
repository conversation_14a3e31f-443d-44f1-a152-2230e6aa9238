#!/bin/bash

validate_environment() {
    local valid_environments=("production" "development")

    # Check if environment.conf exists
    if [ ! -f "environment.conf" ]; then
        echo "Error: environment.conf file not found"
        return 1
    fi

    # Source the environment file
    source environment.conf

    # Validate ENVIRONMENT
    if [ -z "$ENVIRONMENT" ]; then
        echo "Error: ENVIRONMENT not set in environment.conf"
        return 1
    fi

    # Validate if it's a known environment
    local valid_env=0
    for env in "${valid_environments[@]}"; do
        if [ "$ENVIRONMENT" == "$env" ]; then
            valid_env=1
            break
        fi
    done

    if [ $valid_env -eq 0 ]; then
        echo "Error: Invalid ENVIRONMENT '$ENVIRONMENT'. Valid values are: ${valid_environments[*]}"
        return 1
    fi

    # Validate GIT_BRANCH
    if [ -z "$GIT_BRANCH" ]; then
        echo "Error: GIT_BRANCH not set in environment.conf"
        return 1
    fi

    # Additional git branch validation
    if ! git check-ref-format --branch "$GIT_BRANCH" >/dev/null 2>&1; then
        echo "Error: Invalid GIT_BRANCH format: '$GIT_BRANCH'"
        return 1
    fi

    echo "Environment validation successful:"
    echo "ENVIRONMENT: $ENVIRONMENT"
    echo "GIT_BRANCH: $GIT_BRANCH"
    return 0
}
