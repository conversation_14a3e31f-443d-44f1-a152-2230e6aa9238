YUI.add("moodle-availability_group-form",function(t,i){M.availability_group=M.availability_group||{},M.availability_group.form=t.Object(M.core_availability.plugin),M.availability_group.form.groups=null,M.availability_group.form.initInner=function(i){this.groups=i},M.availability_group.form.getNode=function(i){for(var a,e,l='<label><span class="pr-3">'+M.util.get_string("title","availability_group")+'</span> <span class="availability-group"><select name="id" class="custom-select"><option value="choose">'+M.util.get_string("choosedots","moodle")+'</option><option value="any">'+M.util.get_string("anygroup","availability_group")+"</option>",o=0;o<this.groups.length;o++)l+='<option value="'+(a=this.groups[o]).id+'">'+a.name+"</option>";return e=t.Node.create('<span class="form-inline">'+(l+="</select></span></label>")+"</span>"),i.creating===undefined&&(i.id!==undefined&&e.one("select[name=id] > option[value="+i.id+"]")?e.one("select[name=id]").set("value",""+i.id):i.id===undefined&&e.one("select[name=id]").set("value","any")),M.availability_group.form.addedEvents||(M.availability_group.form.addedEvents=!0,t.one(".availability-field").delegate("change",function(){M.core_availability.form.update()},".availability_group select")),e},M.availability_group.form.fillValue=function(i,a){a=a.one("select[name=id]").get("value");"choose"===a?i.id="choose":"any"!==a&&(i.id=parseInt(a,10))},M.availability_group.form.fillErrors=function(i,a){var e={};this.fillValue(e,a),e.id&&"choose"===e.id&&i.push("availability_group:error_selectgroup")}},"@VERSION@",{requires:["base","node","event","moodle-core_availability-form"]});