YUI.add("moodle-availability_grouping-form",function(n,i){M.availability_grouping=M.availability_grouping||{},M.availability_grouping.form=n.Object(M.core_availability.plugin),M.availability_grouping.form.groupings=null,M.availability_grouping.form.initInner=function(i){this.groupings=i},M.availability_grouping.form.getNode=function(i){for(var a,e,l='<label><span class="pr-3">'+M.util.get_string("title","availability_grouping")+'</span> <span class="availability-group"><select name="id" class="custom-select"><option value="choose">'+M.util.get_string("choosedots","moodle")+"</option>",o=0;o<this.groupings.length;o++)l+='<option value="'+(a=this.groupings[o]).id+'">'+a.name+"</option>";return e=n.Node.create('<span class="form-inline">'+(l+="</select></span></label>")+"</span>"),i.id!==undefined&&e.one("select[name=id] > option[value="+i.id+"]")&&e.one("select[name=id]").set("value",""+i.id),M.availability_grouping.form.addedEvents||(M.availability_grouping.form.addedEvents=!0,n.one(".availability-field").delegate("change",function(){M.core_availability.form.update()},".availability_grouping select")),e},M.availability_grouping.form.fillValue=function(i,a){a=a.one("select[name=id]").get("value");i.id="choose"===a?"choose":parseInt(a,10)},M.availability_grouping.form.fillErrors=function(i,a){var e={};this.fillValue(e,a),"choose"===e.id&&i.push("availability_grouping:error_selectgrouping")}},"@VERSION@",{requires:["base","node","event","moodle-core_availability-form"]});