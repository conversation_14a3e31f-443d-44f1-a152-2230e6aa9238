<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language strings.
 *
 * @package availability_completion
 * @copyright 2014 The Open University
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['cachedef_previous_cache'] = 'Previous activity dependency information';
$string['description'] = 'Require students to complete (or not complete) another activity.';
$string['error_selectcmid'] = 'You must select an activity for the completion condition.';
$string['error_selectcmidpassfail'] = 'You must select an activity with "Require grade" completion condition set.';
$string['label_cm'] = 'Activity or resource';
$string['label_completion'] = 'Required completion status';
$string['missing'] = '(Missing activity)';
$string['option_complete'] = 'must be marked complete';
$string['option_fail'] = 'must be complete with fail grade';
$string['option_incomplete'] = 'must not be marked complete';
$string['option_pass'] = 'must be complete with pass grade';
$string['option_previous'] = 'Previous activity with completion';
$string['pluginname'] = 'Restriction by activity completion';
$string['requires_incomplete'] = 'The activity <strong>{$a}</strong> is incomplete';
$string['requires_complete'] = 'The activity <strong>{$a}</strong> is marked complete';
$string['requires_complete_pass'] = 'The activity <strong>{$a}</strong> is complete and passed';
$string['requires_complete_fail'] = 'The activity <strong>{$a}</strong> is complete and failed';
$string['requires_not_complete_pass'] = 'The activity <strong>{$a}</strong> is not complete and passed';
$string['requires_not_complete_fail'] = 'The activity <strong>{$a}</strong> is not complete and failed';
$string['requires_previous_incomplete'] = 'The <strong>previous activity with completion</strong> is incomplete';
$string['requires_previous_complete'] = 'The <strong>previous activity with completion</strong> is marked complete';
$string['requires_previous_complete_pass'] = 'The <strong>previous activity with completion</strong> is complete and passed';
$string['requires_previous_complete_fail'] = 'The <strong>previous activity with completion</strong> is complete and failed';
$string['requires_previous_not_complete_pass'] = 'The <strong>previous activity with completion</strong> is not complete and passed';
$string['requires_previous_not_complete_fail'] = 'The <strong>previous activity with completion</strong> is not complete and failed';
$string['title'] = 'Activity completion';
$string['privacy:metadata'] = 'The Restriction by activity completion plugin does not store any personal data.';
