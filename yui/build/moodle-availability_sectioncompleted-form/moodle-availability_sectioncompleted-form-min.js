YUI.add("moodle-availability_sectioncompleted-form",function(t,e){M.availability_sectioncompleted=M.availability_sectioncompleted||{},M.availability_sectioncompleted.form=t.Object(M.core_availability.plugin),M.availability_sectioncompleted.form.roles=null,M.availability_sectioncompleted.form.initInner=function(e){this.roles=e},M.availability_sectioncompleted.form.getNode=function(e){var i,l="<label>"+M.util.get_string("title","availability_sectioncompleted")+' <span class="availability-group"><select name="id"><option value="choose">'+M.util.get_string("choosedots","moodle")+"</option>";return t.each(this.roles,function(e){l+='<option value="'+e.id+'">'+e.name+"</option>"}),l+="</select></span></label>",i=t.Node.create("<span>"+l+"</span>"),e.id!==undefined&&i.one("select[name=id] > option[value="+e.id+"]")&&i.one("select[name=id]").set("value",""+e.id),M.availability_sectioncompleted.form.addedEvents||(M.availability_sectioncompleted.form.addedEvents=!0,t.one(".availability-field").delegate("change",function(){M.core_availability.form.update()},".availability_sectioncompleted select")),i},M.availability_sectioncompleted.form.fillValue=function(e,i){i=i.one("select[name=id]").get("value");e.id="choose"===i?"choose":parseInt(i,10)},M.availability_sectioncompleted.form.fillErrors=function(e,i){var l={};this.fillValue(l,i),"choose"===l.id&&e.push("availability_sectioncompleted:error_selectrole")}},"@VERSION@",{requires:["base","node","event","moodle-core_availability-form"]});