YUI.add("moodle-availability_role-form",function(a,e){M.availability_role=M.availability_role||{},M.availability_role.form=a.Object(M.core_availability.plugin),M.availability_role.form.roles=null,M.availability_role.form.initInner=function(e){this.roles=e},M.availability_role.form.getNode=function(e){var i,l='<label><span class="pr-3">'+M.util.get_string("title","availability_role")+'</span> <span class="availability-group"><select name="id" class="custom-select"><option value="choose">'+M.util.get_string("choosedots","moodle")+"</option>";return a.each(this.roles,function(e){l+='<option value="'+e.id+'">'+e.name+"</option>"}),l+="</select></span></label>",i=a.Node.create("<span>"+l+"</span>"),e.id!==undefined&&i.one("select[name=id] > option[value="+e.id+"]")&&i.one("select[name=id]").set("value",""+e.id),M.availability_role.form.addedEvents||(M.availability_role.form.addedEvents=!0,a.one(".availability-field").delegate("change",function(){M.core_availability.form.update()},".availability_role select")),i},M.availability_role.form.fillValue=function(e,i){i=i.one("select[name=id]").get("value");e.id="choose"===i?"choose":parseInt(i,10)},M.availability_role.form.fillErrors=function(e,i){var l={};this.fillValue(l,i),"choose"===l.id&&e.push("availability_role:error_selectrole")}},"@VERSION@",{requires:["base","node","event","moodle-core_availability-form"]});