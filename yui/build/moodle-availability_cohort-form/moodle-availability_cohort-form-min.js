YUI.add("moodle-availability_cohort-form",function(l,i){M.availability_cohort=M.availability_cohort||{},M.availability_cohort.form=l.Object(M.core_availability.plugin),M.availability_cohort.form.cohorts=null,M.availability_cohort.form.initInner=function(i){this.cohorts=i},M.availability_cohort.form.getNode=function(i){for(var o,a,t='<label><span class="pr-3">'+M.util.get_string("title","availability_cohort")+'</span> <span class="availability-cohort"><select name="id" class="custom-select"><option value="choose">'+M.util.get_string("choosedots","moodle")+'</option><option value="any">'+M.util.get_string("anycohort","availability_cohort")+"</option>",e=0;e<this.cohorts.length;e++)t+='<option value="'+(o=this.cohorts[e]).id+'">'+o.name+"</option>";return a=l.Node.create('<span class="form-inline">'+(t+="</select></span></label>")+"</span>"),i.creating===undefined&&(i.id!==undefined&&a.one("select[name=id] > option[value="+i.id+"]")?a.one("select[name=id]").set("value",""+i.id):i.id===undefined&&a.one("select[name=id]").set("value","any")),M.availability_cohort.form.addedEvents||(M.availability_cohort.form.addedEvents=!0,l.one(".availability-field").delegate("change",function(){M.core_availability.form.update()},".availability_cohort select")),a},M.availability_cohort.form.fillValue=function(i,o){o=o.one("select[name=id]").get("value");"choose"===o?i.id="choose":"any"!==o&&(i.id=parseInt(o,10))},M.availability_cohort.form.fillErrors=function(i,o){var a={};this.fillValue(a,o),a.id&&"choose"===a.id&&i.push("availability_cohort:error_selectcohort")}},"@VERSION@",{requires:["base","node","event","moodle-core_availability-form"]});