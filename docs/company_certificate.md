### Company Installation Competencies

Admin can create the Company installation competencies:
  - It will be a combination of user installation competencies. For eg. Company installation competency A is created which is combination of user installation competency **competency 1** & **competency 2**
  - A company will be considered to be certified/completed for installation **competency A**, if either any one user from this company has completed both of theses user competency 1 & 2, or any one user has completed **competency 1** and one another user from same company has completed **competency 2**

#### Process to check for company certification/completion for company competencies for users completing user competencies in future
- User 1 completes competency 1
  - Check if company is already certified for the company competency A
    - If company is already certified; process ends.
  - If company is not already certified check if any of the company user **including User 1** has completed **user competency 2**
    - If anyone has completed user competency 2; this company will be marked as certified/completed for company competency A
    - If no one has completed user competency 2; process will repeat when someone from this company completes user competency 2


#### Process to check for company certification/completion for company competencies with already completed user competencies
Suppose a new Company competency X is created with combination of user competency m & n. There are a lot of users who have already completed m and n. In this case we need to compute which companies should be marked certified/completed based on the user completions of user competency m & n.
- This process should run once for each company competency after its creation to mark completion
- We should make a SQL query to find which company's users have completed both m & n either individually or together(means atleast one user has completed m and atleast one user has completed n).
  - If companies are found which users have completed both m & n, either individually or together, these companies should be marked certified/completed for company competency X.