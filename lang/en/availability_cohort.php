<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Availability cohort - Language pack
 *
 * @package     availability_cohort
 * @copyright   2018 Ulm University <<EMAIL>>
 * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['anycohort'] = '(Any cohort)';
$string['description'] = 'Allow only students who belong to a specified cohort.';
$string['error_selectcohort'] = 'You must select a cohort.';
$string['missing'] = '(Missing cohort)';
$string['pluginname'] = 'Restriction by cohort';
$string['privacy:metadata'] = 'The Restriction by cohort plugin does not store any personal data.';
$string['requires_anycohort'] = 'You belong to <strong>any cohort</strong>';
$string['requires_cohort'] = 'You belong to <strong>{$a}</strong>';
$string['requires_notanycohort'] = 'You do <strong>not</strong> belong to <strong>any cohort</strong>';
$string['requires_notcohort'] = 'You do <strong>not</strong> belong to <strong>{$a}</strong>';
$string['title'] = 'Cohort';
