<?php

$string['COMPLETIONTYPE_ALL'] = 'All';
$string['COMPLETIONTYPE_ANY'] = 'Any One';
$string['COMPLETIONTYPE_SOME'] = 'Atleast X';
$string['COMPLETIONTYPE_SOME_X'] = 'Complete Atleast {$a->min_items_to_complete}';
$string['achievement_path_added'] = 'Achievement path added';
$string['achievement_path_completed'] = 'Achievement path completed';
$string['achievement_pathname'] = 'Achievement path name';
$string['achievement_requirement_added'] = 'Achievement requirement added';
$string['achievement_requirement_completed'] = 'Achievement requirement completed';
$string['achievement_requirement_deleted'] = 'Achievement requirement deleted';
$string['achievement_requirements'] = 'Achievement requirements';
$string['add_icompetency'] = 'Add installation competencies';
$string['add_new_set'] = 'Add new set';
$string['add_new_set_of'] = 'Add new set of';
$string['api_sync'] = 'API synced';
$string['apistatus'] = 'Status of the Sync';
$string['competency_description'] = 'Competency description';
$string['competency_fullname'] = 'Competency fullname';
$string['competency_idnumber'] = 'Competency idnumber';
$string['competency_shortname'] = 'Competency shortname';
$string['completion_type'] = 'Completion type';
$string['configure_achievement_path'] = 'Configure achievement path';
$string['configure_achievement_paths'] = 'Configure achievement paths';
$string['configure_achievement_requirements'] = 'Configure achievement requirements';
$string['confirm_icomp_delete'] = 'Are you sure to delete icompetency? It will delete the users installation authorisations.';
$string['created_at'] = 'Created at';
$string['delete_achievement_path'] = 'Delete achievement path';
$string['delete_achievement_path_confirmation'] = 'Are you sure to delete this achievement path? This operation will cause to re-calculate the authorisation completion for this technology for all the users from selected organisations';
$string['delete_achievement_requirement'] = 'Delete achievement requirement';
$string['delete_achievement_requirement_confirmation'] = 'Are you sure to delete this achievement requirement? This operation will cause to re-calculate the authorisation completion for this technology for all the users related to this technology.';
$string['disablecompletioncheck'] = 'Disable completion checking';
$string['edit_achievement_path'] = 'Edit achievement path';
$string['edit_icompetency'] = 'Edit installation competency';
$string['edit_set_ops'] = 'Edit set operators';
$string['entity_icomp'] = 'Icompetency';
$string['entity_icomp_completions'] = 'Icompetency Completions';
$string['error:invalidachievablesetgroupoperator'] = 'Invalid achievable set group operator.';
$string['error:nextachievablesetmissing'] = 'Next achievable set missing.';
$string['error_missing_icompid'] = 'Icompetency id missing';
$string['icomp_completions'] = 'Icompetency Completions';
$string['icomp_idnumber'] = 'Icompetency idnumber';
$string['icomp_idnumber'] = 'Icompetency idnumber';
$string['icomp_initial_task_running_path_not_addable'] = 'Icompetency initial completion task is running so can not allow to add a new path. Please refresh this page in sometime, as the task completes you will be able to add new path';
$string['icomp_initialcheck_done_requirements_not_changeable_description'] = 'Icompetency intial completion check done now requirements can not be modified.';
$string['icomp_name'] = 'Icompetency name';
$string['icomp_shortname'] = 'Icompetency shortname';
$string['icompetency'] = 'Installation competencies';
$string['icompetency_created'] = 'Icompetency created';
$string['icompetency_deleted'] = 'Icompetency deleted';
$string['icompetency_updated'] = 'Icompetency updated';
$string['manage_icompetency'] = 'Manage installation competencies';
$string['manage_cicompetency'] = 'Manage company installation competencies';
$string['add_cicompetency'] = 'Add company competency';
$string['edit_cicompetency'] = 'Edit company competency';
$string['cicompetency'] = 'Company competency';
$string['cicompetency_created'] = 'Company competency created';
$string['cicompetency_updated'] = 'Company competency updated';
$string['cicompetency_deleted'] = 'Company competency deleted';
$string['cicompetency_completed'] = 'Company competency completed';
$string['cicompetency_initial_completion_task'] = 'Company competency initial completion check';
$string['confirm_cicomp_delete'] = 'Are you sure you want to delete this company competency? This will remove all completion records.';
$string['requirements'] = 'Requirements';
$string['add_requirement'] = 'Add requirement';
$string['remove'] = 'Remove';
$string['no_requirements'] = 'No requirements have been added yet.';
$string['new_set_added'] = 'New set added';
$string['next_set_operator'] = 'Next set operator';
$string['overview'] = 'Overview';
$string['path_has_completion_so_not_deletable'] = 'Path has completions so can not delete it';
$string['path_org_not_deletable_description'] = 'As path has completions so its organisations can not be altered';
$string['pluginname'] = 'Installation authorisation';
$string['requirement_has_completions_not_changeable_description'] = 'This requirement has completions, can not modify anything now.';
$string['requirement_itemset_completed'] = 'Requirement item set completed';
$string['save_set_operators_confirmation'] = 'If you change the operators among sets then it will erase completion of path and reaggregate which is an expensive operation.';
$string['scheduled_for_deletion'] = 'Scheduled for deletion';
$string['set_deleted'] = 'Set deleted';
$string['set_deleting_confirmation'] = 'Are you sure to delete this set? This will reaggreagate the user completion of this icompetency, which is a very expensive operation and will take time.';
$string['set_item_deleted'] = 'Set item deleted';
$string['set_item_deleting_confirmation'] = 'Are you sure to delete this set item? This will reaggreagate the user completion of this icompetency, which is a very expensive operation and will take time.';
$string['set_label'] = 'Set label';
$string['set_type'] = 'Set type';
$string['shortname_already_used'] = 'Shortname already used';
$string['sync_to_enlighten'] = 'Sync flag to Enlighten';
$string['timeenrolled'] = 'Enrolment time';
$string['update_achievable_set'] = 'Update set';
$string['updated_at'] = 'Updated at';
$string['cicomp_req_delete'] = 'Delete requirement';
$string['configure_cicomp_comp_requirement_for'] = 'Configure completion requirement for {$a}';
$string['cicomp_req_deleted'] = 'Requirement deleted successfully';
$string['confirm_cicomp_req_delete'] = 'Are you sure to delete this requirement? This will reaggreagate the company completion of this competency';
