<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     quizaccess_extraattempts
 * @category    string
 * @copyright   2024 <PERSON> <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Extra Attempts';
$string['extraattempts_desc_recomplete'] = 'If you fail this quiz with given number of attempts, you will need to revisit {$a} to get new attempts to pass this quiz.';
$string['extraattempts_desc_without_recomplete'] = 'If you fail the quiz with given number of attempts, you will get new attempts to pass this quiz.';
$string['extraattempts_on_failure'] = 'Extra attempts on failure';
$string['taketocmid'] = 'Take to course module';
$string['taketocmid_help'] = 'When user fails the quiz he/she will need to revisit the selected module, after recompletion they will get extra attempts in the failed quiz.';
$string['just_give_extra_attempts'] = 'Just give extra attempts.';
$string['recomplete_for_extraattempts'] = 'Revisit {$a} to get extra attempts.';
$string['attempts_exhausted'] = 'You have exhausted your attempts, please revisit the {$a} to get new attempts to pass this quiz.';
