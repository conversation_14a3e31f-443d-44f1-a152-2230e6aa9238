<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     local_catalog
 * @category    string
 * @copyright   2024 <PERSON> <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['add_system'] = 'Add system';
$string['add_technology'] = 'Add technology';
$string['archive'] = 'Archive';
$string['archived'] = 'Archived';
$string['delete_system'] = 'Delete system';
$string['delete_technology'] = 'Delete technology';
$string['edit_system'] = 'Edit system';
$string['edit_technology'] = 'Edit technology';
$string['errorcreatesystem'] = 'You can not create a system';
$string['errorcreatetech'] = 'You can not create a technology';
$string['errorcreatetrainingneeds'] = 'You can not create training needs for this system';
$string['errordeletesystem'] = 'You can not delete this system';
$string['errordeletetech'] = 'You can not delete this technology';
$string['errordeletetrainingneeds'] = 'You can not delete training for this system';
$string['erroreditsystem'] = 'You can not edit this system';
$string['erroredittech'] = 'You can not edit this technology';
$string['erroredittrainingneeds'] = 'You can not edit training for this system';
$string['invaliddynamictab'] = 'Dynamic tab class is not valid';
$string['manage_system'] = 'Manage system';
$string['manage_training_requirements'] = 'Manage training requirements';
$string['training_requirements'] = 'Training requirements';
$string['manage_trainingneeds'] = 'Manage training needs for system {$a}';
$string['pluginname'] = 'Training requirements';
$string['program'] = 'Program';
$string['s_no'] = 'S.No';
$string['settings:departments'] = 'Training requirements catalog avilable to departments';
$string['settings:departments_help'] = 'Training requirements catalog will be avilable to selected departments and their descendants';
$string['system'] = 'System';
$string['system_code'] = 'System code';
$string['system_created'] = 'System created';
$string['system_deleted'] = 'System deleted';
$string['system_image'] = 'System image';
$string['system_name'] = 'System name';
$string['system_updated'] = 'System updated';
$string['technology'] = 'Technology';
$string['technology_code'] = 'Technology code';
$string['technology_created'] = 'Technology created';
$string['technology_deleted'] = 'Technology deleted';
$string['technology_image'] = 'Technology image';
$string['technology_updated'] = 'Technology updated';
$string['tr:managesystemsall'] = 'Can manage technology system combinations created by anyone';
$string['tr:managesystemsown'] = 'Can manage only those technology system combinations created by self';
$string['tr:managetechall'] = 'Can manage all technologies created by anyone';
$string['tr:managetechown'] = 'Can manage only those technologies created by self';
$string['tr:managetrainingneedsall'] = 'Can configure technoloy system combinations\' training requirements for all systems';
$string['tr:managetrainingneedsown'] = 'Can configure only those technoloy system combinations\' training requirements created by self';
$string['trainingneed_created'] = 'Training need created';
$string['trainingneed_deleted'] = 'Training need deleted';
$string['trainingneed_updated'] = 'Training need updated';

$string['add_trainingneed'] = 'Add training requirement';
$string['edit_trainingneed'] = 'Edit training requirement';
$string['delete_trainingneed'] = 'Delete training requirement';
$string['delete_trainingneed_confirm'] = 'Are you sure to delete this training requirement: {$a}';
$string['deletepreventedsystemalreadyarchived'] = 'Can not delete as system is already archived';
$string['createpreventedsystemalreadyarchived'] = 'Can not add training requirement as system is already archived';
$string['updatepreventedtechalreadyarchived'] = 'Can not update system as technology is already archived';
$string['manage_tech_systems'] = 'Manage systems of technology {$a}';
$string['no_tech_for_department'] = 'There is no technologies available for department';
$string['no_systems_for_tech'] = 'There is no systems available for selected technology';
$string['no_training_requirements_for_system'] = 'There is no trainings available for selected system';
$string['error_department_mismatch'] = 'The department mismatch error';
$string['error_language_mismatch'] = 'The language mismatch error';
$string['tr_page_heading'] = 'Training Requirement - Guide';
$string['tr_choose_your'] = 'Choose your';
$string['tr_tech'] = 'Technology';
$string['tr_sys_conf'] = 'System Configuration';
$string['tr_crs_tech_sys'] = 'Here is a list according to your selection';
$string['prv_step'] = 'Previous step';