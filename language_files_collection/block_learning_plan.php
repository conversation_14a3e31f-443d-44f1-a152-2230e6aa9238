<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * This plugin serves as a database and plan for all learning activities in the organization,
 * where such activities are organized for a more structured learning program.
 * @package    block_learning_plan
 * @copyright  3i Logic<<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL
 * <AUTHOR> <<EMAIL>>
 */
defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Certification program';
$string['learning_plan'] = 'Certification Program';
$string['learning_plans'] = 'Certification Programs';
$string['learning_plan_name'] = 'Training Name';
$string['learningpath'] = 'Add certification program';
$string['desc'] = 'Description';
$string['end_date'] = 'End date';
$string['attachment'] = 'Upload file';
$string['start_date'] = 'Start date';
$string['url'] = 'URL';
$string['add_training'] = 'Add training';
$string['add_training_method'] = 'Add training method';
$string['learningplanstage'] = 'Certification program stage';
$string['assign_training_learningplan'] = 'Assign training';
$string['training_name'] = 'Course name';
$string['learningplan'] = 'Certification program';
$string['training'] = 'Course name';
$string['add_learningplan'] = 'Add certification program';
$string['trainingstatus'] = 'Set training status';
$string['remarks'] = 'Remarks';
$string['status'] = 'Status';
$string['assign_learningplan_user'] = 'Assign certification program to users';
$string['addusers'] = 'Add users';
$string['searchusers'] = 'Search users';
$string['showuser'] = 'Show users';
$string['block/learning_plan:viewpages'] = 'Certification program view pages';
$string['block/learning_plan:managepages'] = 'Certification program manage pages';
$string['myview'] = 'My courses';
$string['search'] = 'Search';
$string['status_report'] = 'Status report';
$string['report_at'] = 'Report as at';
$string['plan_format'] = 'Please add certification program in correct format.';
$string['plan_exist'] = 'Certification Program already exist';
$string['training_format'] = 'Please add  training in correct format';
$string['training_exist'] = 'Training already exist.';
$string['date_val'] = 'End date should be greater than start date.';
$string['s_no'] = 'S.No.';
$string['wrong_url'] = 'Wrong URL';
$string['elearning'] = 'eLearning';
$string['classroom'] = 'Classroom training';
$string['onthejob'] = 'On the job training';
$string['assignee'] = 'Assignee';
$string['remove'] = 'Remove';
$string['edit'] = 'Edit';
$string['setting'] = 'Setting';
$string['plan_delete'] = 'Do you want to delete certification program?';
$string['training_delete'] = 'Do You Want to Delete training?';
$string['record_delete'] = 'Do you want to delete record?';
$string['learning_plan:addinstance'] = 'Add instance';
$string['learning_plan:managepages'] = 'Manage pages';
$string['learning_plan:viewpages'] = 'View pages';
$string['user'] = 'User';
$string['users'] = 'Users';
$string['group_selection'] = 'Group selection';
$string['department'] = 'Group';
$string['status_all'] = 'All Status';
$string['status_not_started'] = 'Not Yet Started';
$string['status_in_progress'] = 'In-Progress';
$string['status_completed'] = 'Completed';
$string['status_all'] = 'All Status';
$string['saved'] = 'Record Added';
$string['updated'] = 'Record Updated';
$string['removed'] = 'Record Removed';
$string['saved_changes'] = 'Saved Changes';
$string['select_training'] = 'Please select training(s)';
$string['select_user'] = 'Please select user(s)';
$string['user_training'] = 'Please select training';
$string['selectuser'] = 'Please select user';
$string['select_learningplan'] = 'Please select Certification Program';
$string['notfound'] = 'Record not found!';
$string['messageprovider:learningplan_notification'] = 'Certification program notification';
$string['learninig_plan:learningplan_notification'] = 'View message';
$string['send_notification'] = 'Notification';
$string['message'] = 'Message';
$string['send_message'] = 'Send message';
$string['notification_sent'] = 'Notification sent';
$string['learning_plan_error'] = 'Certification program is empty';
$string['messageprovider:view'] = 'View notification';
$string['learninig_plan:view'] = 'Certification program notification view';
$string['learninig_plan:sendmessages'] = 'Send notification';
$string['learninig_plan:viewmessages'] = 'View notification';
$string['learninig_plan_stage1'] = 'Level 1';
$string['learninig_plan_stage2'] = 'Level 2';
$string['learninig_plan_stage3'] = 'Level 3';
$string['learninig_plan_stage4'] = 'Level 4';
$string['learninig_plan_stage5'] = 'Level 5';
$string['cohorts'] = 'Cohorts';
$string['assign_method'] = 'Assign method';
$string['assign_learningplan_on_roles'] = 'Assign certification program based on roles';
$string['roles'] = 'Roles';
$string['approverequests'] = 'Approve certification program requests';
$string['region'] = 'Region';
$string['employeetype'] = 'Course track';
$string['managemicrolearning'] = 'Manage micro learning';
$string['microlearningname'] = 'Micro learning name';
$string['linkmodules'] = 'Link modules';
$string['selectcourse'] = 'Select course';
$string['selectmodules'] = 'Select modules';
$string['selectmicrolearningmodule'] = 'Select microleaarning modules';
$string['trainingtype'] = 'Training type';
$string['emailsubject'] = 'Email subject';
$string['emailbody'] = 'Email body';
$string['parent'] = 'Parent';
$string['completionemail'] = 'Completion email';
$string['coursetype'] = 'Course type';
$string['coursecat'] = 'Course Category';
$string['configureemails'] = 'Configure emails';
$string['notificationtype'] = 'Notification type';
$string['crontask'] = 'Add on course notifications';
$string['autoenroll'] = 'Auto Enroll to badges Certification Program';
$string['courses'] = 'Courses';
$string['email'] = 'Email ID';
$string['sendtoapi'] = 'Approve for API';
$string['handsontraining'] = 'Hands on Training';
///// restriction set lang strings ////////
$string['restriction_set'] = 'Restriction set';
$string['restriction_type'] = 'Restriction type';
$string['manage_restrictions'] = 'Manage restrictions';
$string['edit_restrictions'] = 'Edit learningplan restrictions';
$string['restrictionset_num'] = 'Restriction set number {$a}';
$string['nextsetoperator'] = 'Next set operator';
$string['addnew_restrictionset'] = 'Add new restriction set';
$string['restriction_form_cancelled'] = 'Restriction edit/add cancelled';
$string['delete_restriction_set'] = 'Delete restriction set';
$string['pre_requisite'] = 'Pre-requisite';
$string['delete_lp_restrictions'] = 'Delete restrictions for certification program {$a}';
$string['delete_lp_restrictions_check'] = 'Are you sure to delete restrictions for certification program {$a}';
$string['restriction_sets_delete_success'] = 'Restriction sets deleted successfully.';
$string['restriction_sets_delete_failed'] = 'Sorry, Restriction sets couldn\'t be deleted.';

/// course set lang strings
$string['nextcourseoperator'] = 'Next course operator';
$string['remove_course'] = 'Remove course';
$string['addnew_course'] = 'Add course';
$string['course_already_added'] = 'This course is already existing in this certification program';
$string['edit_lp_content'] = 'Edit certification program content';
$string['lp_content_updated'] = 'Certification program content updated successfully';
$string['select_countries'] = 'Select countries';
$string['restriction_sets_update_success'] = 'Restriction sets updated successfully';
$string['restriction_sets_update_failed'] = 'Restriction sets couldn\'t be updated';
$string['lp_content'] = 'Certification Program Content';
$string['learning_plan_viewed'] = 'Certification program viewed';
$string['invalid_restriction_set'] = 'Invalid restriction set';
$string['invalid_restriction_set_course'] = 'Invalid restriction set course';
$string['no_learning_plans_access'] = 'You don\'t have access to any certification program';
$string['learning_plan_not_enrolled'] = 'You don\'t have access to this certification program';
$string['no_content_available'] = 'This certification program has no content';
$string['stage_has_no_content_available'] = 'This certification program stage has no content';
$string['previous_level_incomplete'] = 'Please complete stage {$a} before moving ahead';
$string['stage_course_not_available'] = 'This certification program stage does not contain requested course';
$string['level_up_cron'] = 'Hi you are moving up to next stage of this certification program, please wait while we are running cron to mark all previous completions for you.';
$string['level_up_cron_completed'] = 'Level Up cron execution completed. Now you will be redirected';
$string['country'] = 'Country';
$string['levelnum'] = 'Level {$a}';
$string['invalidhash'] = 'Sorry, {$a} is not a valid hash.';
$string['invalid_restriction_type'] = 'Invalid restriction type';
$string['previous_rset_incomplete'] = 'Previous restriction set incomplete.';
$string['previous_requirements_incomplete'] = 'Please complete previous requirements.';
$string['complete_previous_courses_for_access'] = 'Please complete previous courses in the certification program before accessing this.';
$string['setting_up_next_course'] = 'Please wait we are setting up the next course for you.';
$string['setting_up_next_course_completed'] = 'Setting Up of next course completed. Now you will be redirected.';
$string['setting_up_next_course_completed_continue'] = 'Setting Up of next course completed. Please click continue button to go to next course.';
$string['pre-requisite-completion-needed'] = 'You will need to complete these pre-requisite before you could access the content';

$string['setting_up_next_course_failed'] = 'Next course setup could not be completed ';
$string['setting_up_next_course_failed_continue'] = 'Next course setup could not be completed. Please click continue to go back to certification program';
$string['go_to_lp_continue'] = 'Please click continue to go to certification program';
$string['now_access_next_module'] = 'Now you can access next module.';

$string['levels'] = 'Levels';

// new string added
$string['then'] = 'then';
$string['approvedusers'] = 'Approved Users';
$string['content_audio_languages'] = 'Content audio languages';
$string['content_subtitle_languages'] = 'Content subtitle languages';
$string['lp_thumbnail'] = 'Learning plan thumbnail image';
$string['sync_to_enlighten'] = 'Sync Progress to Enlighten';
$string['apistatus'] = 'Status of the Sync';
$string['progress'] = 'Progress in %';
$string['synctime'] = 'Synced on';
$string['enlightenhost'] = 'Enlighten Host';
$string['enlightenhost_desc'] = 'Enter the Enlighten Host URL';
$string['auth_host'] = 'Auth Host';
$string['auth_host_desc'] = 'Enter the Auth Host URL';
$string['api_key'] = 'API Key';
$string['api_key_desc'] = 'Enter the API Key which is used for connection with Enlighten';
$string['client_id'] = 'Client ID';
$string['client_id_desc'] = 'Enter the Client ID for onnecting with Enlighten';
$string['client_secret'] = 'Client Secret';
$string['client_secret_desc'] = 'Enter the secret key for the client ID';
$string['enlighten_username'] = 'Enlighten Username';
$string['enlighten_username_desc'] = 'Enter the Enlighten Username';
$string['enlighten_password'] = 'Enlighten Password';
$string['enlighten_password_desc'] = 'Enter the Enlighten Password';
$string['log_folder'] = 'Log Folder Path';
$string['log_folder_desc'] = 'Enter the folder path where the logs are saved';
$string['approverequestconfirmation'] = 'Are you sure to approve the request from the user {$a->fullname}?';
$string['rejectrequestconfirmation'] = 'Are you sure to disapprove the request from the user {$a->fullname}?';

// new strings added on 06-02-2023
$string['enrol_to_lp'] = 'Enroll into certification';
$string['learning_plan:add_learning_plan'] = 'Can add learning plans';
$string['learning_plan:manage_learning_plan'] = 'Can edit and assign training in learning plan';
$string['learning_plan:approve_lp_request'] = 'Can approve learning plan request';
$string['learning_plan:enrol_to_lp'] = 'Can enroll users into a learning plan';
$string['learning_plan_created'] = 'Learning plan created';
$string['learning_plan_updated'] = 'Learning plan updated';
$string['learning_plan_deleted'] = 'Learning plan deleted';
$string['learning_plan_deleted_failed'] = 'Learning plan deletion failed';
$string['training_delete_failed'] = 'Taining deletion failed';
$string['training_added'] = 'Taining added';
$string['training_updated'] = 'Taining updated';
$string['training_deleted'] = 'Taining deleted';
$string['lp_request_updated'] = 'Certification program request updated';
$string['lp_request_exists_already'] = 'User already exists in this program';
$string['approved_by'] = 'Approved by';
$string['cli:unknown_option'] = 'Unrecognised option: \n  {$a} \nPlease use --help option.';

$string['search:learning_plans'] = 'Certification Programs';
$string['learning_plan:can_view_lpreport'] = 'Can view certification program completion report?';
$string['prereq-overrides'] = 'Pre-reqisite overrides';
$string['prereq-override-added'] = 'Pre-reqisite override added';
$string['prereq-override-updated'] = 'Pre-reqisite override updated';
$string['prereq-override-deleted'] = 'Pre-reqisite override deleted';
$string['delete-prereq-override'] = 'Delete pre-reqisite override';
$string['delete-prereq-override-confirmation'] = 'Are you sure to delete this pre-reqisite override';
$string['learning_plan:can_override_prerequisite'] = 'Can override pre-reqisite';
$string['add_prereq_override'] = 'Add pre-reqisite override';
$string['content_navigation_free'] = 'Content navigation free';
$string['content_navigation_free_help'] = 'Content navigation free will allow the user to access content without completing previous content';
$string['lp_country_not_allowed'] = 'Dear user, the course you are trying to access is not applicable in {$a}. Please enrol for your country specific courses from the catalog page.';

$string['error:prognotmoved'] = 'Program not moved';

$string['learning_plan:viewprogram'] = 'Can view learning plan';
$string['learning_plan:viewhiddenprograms'] = 'Can view hidden learning plan';
$string['learning_plan:accessanyprogram'] = 'Can access any learning plan';
$string['learning_plan:createprogram'] = 'Can create learning plan';
$string['learning_plan:deleteprogram'] = 'Can delete learning plan';
$string['learning_plan:configuredetails'] = 'Can configure learning plan details';
$string['learning_plan:configurecontent'] = 'Can configure learning plan content';
$string['learning_plan:configureassignments'] = 'Can configure learning plan assignments';
$string['learning_plan:configuremessages'] = 'Can configure learning plan messages';
$string['learning_plan:visibility'] = 'Can configure learning plan visibility';
$string['manage_certification_programs'] = 'Manage certification programs';
$string['programcategory'] = 'Certification program category';
$string['programcategory_help'] = 'Your Enphase administrator may have set up several program/course categories.

For example, \'Human Resources\', \'Software development\', \'Marketing\' etc.

Choose the one most applicable for your program. This choice will affect where your program is displayed on the program listing and may make it easier for learners to find your program.';
$string['overview'] = 'Overview';
$string['edit_program_details'] = 'Edit details';


$string['availablefrom'] = 'Available From';
$string['availableuntil'] = 'Available Until';
$string['programavailability_help'] = 'This option allows you to hide your program completely.

It will not appear on any program listings, except to administrators.

Even if learners try to access the program URL directly, they will not be allowed to enter.

If you set the **Available from** and **Available until** dates, learners will be able to find and enter the program during the period specified by the dates but will be prevented from accessing the program outside of those dates.';
$string['programvisibility_help'] = 'If the program is visible, it will appear in program listings and search results and learners will be able to view the program contents.

If the program is not visble, it will not appear in program listings or search results but the program will still be displayed in the learning plans of any learners who have been assigned to the program and learners can still access the program if they know the program\'s URL.';
$string['visible'] = 'Visible';
$string['availabilitycheckstask'] = 'Program availability checks';

$string['restrictions_created'] = 'Learning plan restrictions created';
$string['restrictions_deleted'] = 'Learning plan restrictions deleted';
$string['restriction_set_updated'] = 'Learning plan restriction set updated';
$string['manage_enrolments'] = 'Manage enrolments';
$string['program_not_available'] = 'Sorry, this learning is not available at present moment, please contact your administrator';

$string['delete-enrolment'] = 'Delete enrolment';
$string['delete-enrolment-confirmation'] = 'Are you sure to delete this enrolment ?';

$string['event:lp_request_created'] = 'Learning plan request created';
$string['event:lp_request_created_desc'] = 'The user with id {$a->userid} has created training request for program with id {$a->lp_id}';
$string['event:lp_request_deleted'] = 'Learning plan request deleted';
$string['event:lp_request_deleted_desc'] = 'The user with id {$a->userid} has updated training request for program with id {$a->lp_id}';

$string['task:lplink'] = 'Parallel learning plan';
$string['page:lplink_edit_title'] = 'Edit Parallel linking';
$string['page:lplink_edit_heading'] = 'Edit Parallel linking';
$string['page:lplink_edit_breadcrumb'] = 'Edit Parallel linking';
$string['page:lplink_add_title'] = 'Add Parallel linking';
$string['page:lplink_add_heading'] = 'Add Parallel linking';
$string['page:lplink_add_breadcrumb'] = 'Add Parallel linking';
$string['notification:cancel'] = 'Action has been successfully cancelled.';
$string['notification:success'] = 'Action has been successfully completed.';
$string['page:lplink_manage_title'] = 'Manage Parallel linking';
$string['page:lplink_manage_heading'] = 'Manage Parallel linking';
$string['page:lplink_manage_breadcrumb'] = 'Manage Parallel linking';
$string['enrolled_lpid_help'] = 'Learning program where user is already enrolled.';
$string['enrolled_lpid_help'] = 'Learning program where user is already enrolled.';
$string['enrol_into_lpid_help'] = 'Learning program where user should be enrolled.';
$string['lplink_active_help'] = 'Parallel link should be active';
$string['enrolled_lp'] = 'Enrolled Learning Plan';
$string['enrol_into_lp'] = 'Enrol into Learning Plan';

$string['category'] = 'Course category';
$string['selectcoursetype'] = 'Select Learning plan type';
$string['certificationcourse'] = 'Certification Program';
$string['advancedcourse'] = 'Qualification Course';
$string['advancedcourses'] = 'Qualification Courses';
$string['handson'] = 'Hands on Training';
$string['standalone'] = 'Elective Course';
$string['alltypes'] = 'All Course Types';
$string['programavailability'] = 'Learning plan availability';
$string['programvisibility'] = 'Learning plan visibility';
$string['role'] = 'Learning plan role';
$string['product'] = 'Learning plan product';
$string['tagarea_learning_product'] = 'Learning plan products';
$string['tagarea_learning_role'] = 'Learning plan roles';
$string['search_by_ufields'] = 'Search by email or name';
$string['status_requested'] = 'Requested';
$string['sac_requests'] = 'Stand alone courses requests';
$string['sortorder'] = 'Sort Order';
$string['cpc_role_settings'] = 'Certification Program role';
$string['role_for_cpc_enroll'] = 'Role for certification program';
$string['role_for_cpc_enroll_desc'] = 'Role for assignment in a certification program when requested by users.';
$string['entity_lp'] = 'Entity learning plan';
$string['minreqsets'] = '{$a->min_reqsets_count}{$a->plus} Sets';
$string['minreqcourses'] = '{$a->min_reqcourse_count}{$a->plus} Courses';
$string['minreqset'] = '{$a->min_reqsets_count} Set';
$string['minreqcourse'] = '{$a->min_reqcourse_count} Course';
$string['entity_lpa'] = 'Entity learning plan availability';
$string['id'] = 'Id';
$string['qc_content'] = 'Qualification Course Content';
$string['lp_completion'] = 'Certification program completion';
$string['entity_lpr'] = 'Learning plan requests';
$string['entity_ulp'] = 'User learning progress';
$string['timecompleted'] = 'Completion time';
$string['progress'] = 'Progress';
$string['lp_completions'] = 'Learning Plan Completions';
$string['viewmore'] = 'View More';
$string['requestaccess'] = 'Request Access';
$string['approved_by'] = 'Approved by';
$string['gotodashboard'] = 'Go to Dashboard';
$string['enrolreqsuccess'] = 'Enrolment requested successfully';
$string['enrolledsuccess'] = 'Enrolled successfully';
$string['learning_plan_request_pending'] = 'Thank you for your enrollment request. Your request has been submitted successfully. Shortly you will receive an email acknowledging your submission.';
$string['learning_plan_request_autoapproved'] = 'Thank you for the enrollment request. Your request has been submitted successfully. The requested course is now available in your dashboard.';
$string['readmore'] = 'Read more';
$string['sessionfull'] = 'You can not signup for the session as the seats are filled.';
$string['requested_non_lp'] = 'Non Certification program/Qualifying course requested';
$string['request_lp_approved_enrolled'] = 'Thank you for the enrollment request. Your request has been submitted successfully. The requested course is now available in your dashboard.';
$string['request_lp_not_processed'] = 'Certification program/Qualifying course request could not be processed';
$string['request_lp_enrollment_error'] = 'Sorry, some enrollment error has occurred. Please contact administrator';
$string['request_lp_approval_waiting'] = 'Thank you for your enrollment request. Your request has been submitted successfully. Shortly you will recieve an email acknowledging your submission.';

$string['msg_for_requester'] = '<table style="border:0;border-spacing: 0;  font-family: Roboto, Arial, sans-serif; font-size:14px; line-height:18px; text-align:center; border-collapse: collapse;width: 100%;max-width: 600px; margin:7px auto;">
		<tr>
		<td>
		<table style="border:0; border-spacing: 0; border-collapse: collapse; width: 100%;margin-top: 0px">
		<tr>
		<td  style="text-align: center; background-color: #3a3a3a; height: 50px; padding: 0;">
		<a href="http://university.enphase.com/"  target="_blank">
		<img src="https://cdn2.hubspot.net/hub/2448117/hubfs/EnphaseEnergy_Sep2016/Images/email-template-logo.png"  alt="Enphase" style="border: 0; margin: 1px auto 0; max-width: 140px; display: inline-block;">
		</a>
		</td>
		</tr>
		</table>

		<table style="border-spacing: 0; border-collapse: collapse; width: 100%;">

		<tr>
		<td style="text-align:left; background-color:#fff;padding: 20px 35px 40px 35px;">

		<p style="font-size: 15px; line-height: 20px; font-family: Roboto, Arial, sans-serif; color: #666666;">Greetings! </p>
		<p style="font-size: 15px; line-height: 20px; font-family: Roboto, Arial, sans-serif; color: #666666;">Thank you for enrolling with Enphase University. We have received your request, and it is currently under review. We will get back to you within 1 business day (IST) confirming your Learning Program enrollment. Once your Learning Program enrollment has been confirmed you can start your courses!</p>

		<p style="font-size: 15px; line-height: 20px; font-family: Roboto, Arial, sans-serif; color: #666666;">Please do not respond to this email as this is an auto-generated response. For any additional questions please reach out to us at <a href="mailto:{$a}" style="color: #f37321;text-decoration: none;">{$a} </a>. </p>

		<p style="font-size: 15px; line-height: 20px; font-family: Roboto, Arial, sans-serif; color: #666666;">Regards,<br>
Enphase University</p>
		</td>
		</tr>

		</table>
		<table style="border:0; background-color:#F0F0F0; color:#a8a8a8; font-family:Roboto, Arial,  sans-serif; font-size:14px; line-height:18px; text-align:left; margin-top: 10px; padding-top: 10px; border-collapse: collapse;width: 600px">

		<tr>
		<td style="padding: 10px 20px; margin: 10px auto; border-collapse: collapse;font-size: 0">
		<div style="display: block;">

		<div style="width: 100%; max-width: 385px; text-align: left; display: inline-block;">
		<table style="border-spacing: 0; border-collapse: collapse; padding: 0; margin: 0;">
		<tr>
		<td style="padding: 0; width: 60px; margin: 0; border-collapse: collapse; font-size: 12px; color: #a8a8a8; line-height: 21px;">
		<a href="http://university.enphase.com" target="_blank" style="color: #f37321; text-decoration: none;  display: block; margin-right: 10px; border-style: none;">
		<img style="display: block; margin-right: 10px; border-style: none;width: 40px" src="http://cdn2.hubspot.net/hubfs/2448117/EnphaseEnergy_Sep2016/Images/email-template-logo-icon.png" alt="Enphase"/>
		</a>
		</td>
		<td  style="vertical-align: middle; padding: 5px 0 0; margin: 0; border-collapse: collapse; font-size: 12px; line-height: 21px; color: #a8a8a8 !important;">
		<p style="font-size: 12px; line-height: 20px; font-family: Roboto, Arial, sans-serif; color: #a8a8a8 !important;"><span style="color: #a8a8a8;"><strong>Enphase Energy, Inc.</strong>
		&copy; 2020<br />47281 Bayside Pkwy, Fremont, CA</span></p>
		</td>
		</tr>
		</table>
		</div>

		<div  style="width: 100%; max-width: 175px; display: inline-block; vertical-align: top;">
		<table style="border-spacing: 0; border-collapse: collapse;  padding: 0; margin: 0;">
		<tr>
		<td style="padding: 0; margin: 0; border-collapse: collapse; text-align: right;">
		<table style="line-height: 1; width: 175px; border:0;" cellpadding="0" cellspacing="0">
		<tr>
		<td>
		<p><a href="https://www.facebook.com/EnphaseEnergy" target="_blank" style="text-decoration: none; display: inline-block;"><img src="http://cdn2.hubspot.net/hubfs/2448117/EnphaseEnergy_Sep2016/Images/email-template-icon-email-social-facebook.png" alt="Enphase On Facebook" style="display: block; margin: 24px 0 0 10px; border: 0; max-width: 34px;" /></a></p>
		</td>
		<td>
		<p><a href="https://twitter.com/Enphase" target="_blank" style="text-decoration: none; display: inline-block;"><img src="http://cdn2.hubspot.net/hubfs/2448117/EnphaseEnergy_Sep2016/Images/email-template-icon-email-social-twitter.png" alt="Enphase On Twitter" style="display: block; margin: 24px 0 0 10px; border: 0; max-width: 34px;" /></a></p>
		</td>
		<td>
		<p><a href="https://www.youtube.com/user/EnphaseEnergy" target="_blank" style="text-decoration: none; display: inline-block;"><img src="http://cdn2.hubspot.net/hubfs/2448117/EnphaseEnergy_Sep2016/Images/email-template-icon-email-social-youtube.png" alt="Enphase On Youtube" style="display: block; margin: 24px 0 0 10px; border: 0; max-width: 34px;" /></a></p>
		</td>
		<td>
		<p><a href="https://www.linkedin.com/company/enphase-energy" target="_blank" style="text-decoration: none; display: inline-block;"><img src="http://cdn2.hubspot.net/hubfs/2448117/EnphaseEnergy_Sep2016/Images/email-template-icon-email-social-linkedin.png" alt="Enphase On Linkedin" style="display: block; margin: 24px 0 0 10px; border: 0; max-width: 34px;" /></a></p>
		</td>
		</tr>
		</table>
		</td>
		</tr>
		</table>
		</div>

		</div>
		</td>
		</tr>
		</table>

		</td>
		</tr>
		</table>';
$string['lpid'] = 'lpid';
$string['search_program'] = 'Search program';
$string['event:lp_sets_updated'] = 'Learning plan set updated';
$string['event:lp_sets_updated_desc'] = 'Learning plan set id {$a->id} of lpid {$a->lpid} has been updated by userid {$a->userid}.';
$string['event:lp_sets_deleted'] = 'Learning plan set deleted';
$string['event:lp_sets_deleted_desc'] = 'Learning plan set id {$a->id} of lpid {$a->lpid} has been deleted by userid {$a->userid}.';
$string['event:lp_sets_created'] = 'Learning plan set created';
$string['event:lp_sets_created_desc'] = 'Learning plan set id {$a->id} of lpid {$a->lpid} has been created by userid {$a->userid}.';
$string['event:lp_set_items_created'] = 'Learning plan set item created';
$string['event:lp_set_items_created_desc'] = 'Learning plan set item id {$a->id} of lpsetid {$a->lpset_id} of lpid {$a->lpid} has been created by userid {$a->userid}.';
$string['event:lp_set_items_deleted'] = 'Learning plan set deleted';
$string['event:lp_set_items_deleted_desc'] = 'Learning plan set item id {$a->id} of lpsetid {$a->lpset_id} of lpid {$a->lpid} has been deleted by userid {$a->userid}.';
$string['event:lp_set_items_updated'] = 'Learning plan set updated';
$string['event:lp_set_items_updated_desc'] = 'Learning plan set item id {$a->id} of lpsetid {$a->lpset_id} of lpid {$a->lpid} has been updated by userid {$a->userid}.';
$string['event:lp_set_completion_updated'] = 'LP set completion updated';
$string['event:lp_set_completion_updated_desc'] = 'LP set completion with set id {$a->set_id} of userid {$a->relateduserid} has been updated by userid {$a->userid}.';
$string['event:lp_set_completion_deleted'] = 'LP set completion deleted';
$string['event:lp_set_completion_deleted_desc'] = 'LP set completion with set id {$a->set_id} of userid {$a->relateduserid} has been deleted by userid {$a->userid}.';
$string['event:lp_set_completion_created'] = 'LP set completion created';
$string['event:lp_set_completion_created_desc'] = 'LP set completion with set id {$a->set_id} of userid {$a->relateduserid} has been created by userid {$a->userid}.';
$string['event:lp_set_item_completion_updated'] = 'LP set item completion updated';
$string['event:lp_set_item_completion_updated_desc'] = 'LP set item completion with item id {$a->item_id} of userid {$a->relateduserid} has been updated by userid {$a->userid}.';
$string['event:lp_set_item_completion_deleted'] = 'LP set item completion deleted';
$string['event:lp_set_item_completion_deleted_desc'] = 'LP set item completion with item id {$a->item_id} of userid {$a->relateduserid} has been deleted by userid {$a->userid}.';
$string['event:lp_set_item_completion_created'] = 'LP set item completion created';
$string['event:lp_set_item_completion_created_desc'] = 'LP set item completion with item id {$a->item_id} of userid {$a->relateduserid} has been created by userid {$a->userid}.';
$string['no_courselink'] = 'Locked';
$string['not_available'] = 'NA';
$string['enrolment_failed'] = 'Enrolment failed, please contact the administrator.';
$string['lpname_with_link'] = 'LP name with link';
$string['lp_info'] = 'Learning plan info';

$string['next_suggestion'] = 'Continue your program';
$string['you_can_goto'] = 'You can go to';
$string['tag_suggestions_placeholder'] = 'Type...';
$string['tagarea_learning_learningplan'] = 'Learning plan tags';
$string['sets'] = 'Sets';
$string['sortorder'] = 'Sortorder';
$string['search_lp']='Search by keywords';
$string['sel_language']='Select the language';
