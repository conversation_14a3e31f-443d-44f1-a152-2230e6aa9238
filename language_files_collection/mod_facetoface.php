<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Copyright (C) 2007-2011 Catalyst IT (http://www.catalyst.net.nz)
 * Copyright (C) 2011-2013 Totara LMS (http://www.totaralms.com)
 * Copyright (C) 2014 onwards Catalyst IT (http://www.catalyst-eu.net)
 *
 * @package    mod
 * @subpackage facetoface
 * @copyright  2014 onwards Catalyst IT <http://www.catalyst-eu.net>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> Marier <<EMAIL>>
 */

$string['addmanageremailaddress'] = 'Add manager email address';
$string['addmanageremailinstruction'] = 'You have not previously entered your manager\'s email address. Please enter it below to sign-up for this session. ';
$string['addsession'] = 'Add a new session';
$string['addingsession'] = 'Adding a new session in {$a}';
$string['addnewfield'] = 'Add a new custom field';
$string['addnewfieldlink'] = 'Create a new custom field';
$string['addnewnotice'] = 'Add a new site notice';
$string['addnewnoticelink'] = 'Create a new site notice';
$string['addremoveattendees'] = 'Add/remove attendees';
$string['addstudent'] = 'Add student';
$string['alllocations'] = 'All locations';
$string['allowcancellations'] = 'Allow sign-up cancellations';
$string['allowcancellationsdefault'] = 'Allow sign-up cancellations default';
$string['allowcancellationsdefault_help'] = 'If checked, the default setting, when creating new sessions for this activity, will be to allow sign-up cancellations';
$string['allowcancellations_help'] = 'If checked, session attendees will be able to cancel their signups';
$string['allowoverbook'] = 'Allow overbooking';
$string['allsessionsin'] = 'All sessions in {$a}';
$string['alreadysignedup'] = 'You have already signed-up for this Face-to-Face activity.';
$string['answer'] = 'Sign in';
$string['answercancel'] = 'Sign out';
$string['approvalreqd'] = 'Approval required';
$string['approve'] = 'Approve';
$string['assessmentyour'] = 'Your assessment';
$string['attendance'] = 'Attendance';
$string['attendanceinstructions'] = 'Select users who attended the session:';
$string['attendedsession'] = 'Attended session';
$string['attendees'] = 'Attendees';
$string['attendeesexporttofileheading'] = 'Attendees export to file';
$string['potentialattendees'] = 'Potential Attendees';
$string['booked'] = 'Booked';
$string['bookingcancelled'] = 'Your booking has been cancelled.';
$string['bookingcompleted'] = 'Your booking has been completed.';
$string['bookingfull'] = 'Booking full';
$string['bookingopen'] = 'Booking open';
$string['bookingstatus'] = 'You are booked for the following session';
$string['calendareventdescriptionbooking'] = 'You are booked for this <a href="{$a}">Face-to-Face session</a>.';
$string['calendareventdescriptionsession'] = 'You have created this <a href="{$a}">Face-to-Face session</a>.';
$string['calendaroptions'] = 'Calendar options';
$string['cancelbooking'] = 'Cancel booking';
$string['cancelbookingfor'] = 'Cancel booking for {$a}';
$string['cancellationsent'] = 'You should immediately receive a cancellation email.';
$string['cancellationsentmgr'] = 'You and your manager should immediately receive a cancellation email.';
$string['cancellationstablesummary'] = 'List of people who have cancelled their session signups.';
$string['cancelreason'] = 'Reason';
$string['capacity'] = 'Capacity';
$string['changemanageremailaddress'] = 'Change manager email address';
$string['changemanageremailinstruction'] = 'Please enter the email address for your current manager below.';
$string['cannotsignupsessioninprogress'] = 'You cannot sign up, this session is in progress';
$string['cannotsignupsessionover'] = 'You cannot sign up, this session is over.';
$string['cannotapproveatcapacity'] = 'You cannot approve any more attendees as this session is full.';
$string['clearall'] = 'Clear all';
$string['closed'] = 'Closed';
$string['conditions'] = 'Conditions';
$string['conditionsexplanation'] = 'All of these criteria must be met for the notice to be shown on the training calendar:';
$string['confirm'] = 'Confirm';
$string['confirmanager'] = 'Confirm manager\'s email address';
$string['confirmmanageremailaddress'] = 'Confirm manager email address';
$string['confirmmanageremailaddressquestion'] = 'Is <b>{$a}</b> still your manager\'s email address?';
$string['confirmmanageremailinstruction1'] = 'You previously entered the following as your manager\'s email address:';
$string['confirmmanageremailinstruction2'] = 'Is this still your manager\'s email address?';
$string['confirmation'] = 'Confirmation';
$string['confirmationmessage'] = 'Confirmation message';
$string['confirmationsent'] = 'You should immediately receive a confirmation email.';
$string['confirmationsentmgr'] = 'You and your manager should immediately receive a confirmation email.';
$string['copyingsession'] = 'Copying as a new session in {$a}';
$string['copysession'] = 'Copy session';
$string['continuetologin'] = 'Click continue to login';
$string['copy'] = 'Copy';
$string['cost'] = 'Cost';
$string['cancelbooking'] = 'Cancel booking';
$string['cancellation'] = 'Cancellation';
$string['cancellations'] = 'Cancellations';
$string['cancellationmessage'] = 'Cancellation message';
$string['cancellationconfirm'] = 'Are you sure you want to cancel your booking to this session?';
$string['crontask'] = 'Face-to-Face maintenance jobs';
$string['costheading'] = 'Session Cost';
$string['currentstatus'] = 'Current status';
$string['customfieldsheading'] = 'Custom Session Fields';
$string['date'] = 'Date';
$string['dateadd'] = 'Add a new date';
$string['dateremove'] = 'Remove this date';
$string['datetext'] = 'You are signed in for date';
$string['datetimeknownhinttext'] = '';
$string['decidelater'] = 'Decide Later';
$string['delete'] = 'Delete';
$string['deleteall'] = 'Delete all';
$string['deletesession'] = 'Delete session';
$string['deletesessionconfirm'] = 'Are you completely sure you want to delete this session and all sign-ups for this session?';
$string['deletingsession'] = 'Deleting session in {$a}';
$string['decline'] = 'Decline';
$string['description'] = 'Introduction text';
$string['details'] = 'Details';
$string['discountcode'] = 'Discount code';
$string['discountcost'] = 'Discount cost';
$string['discountcosthinttext'] = '';
$string['due'] = 'due';
$string['duration'] = 'Duration';
$string['early'] = '{$a} early';
$string['edit'] = 'Edit';
$string['editsession'] = 'Edit session';
$string['editingsession'] = 'Editing session in {$a}';
$string['emailmanager'] = 'Send notice to manager';
$string['email:instrmngr'] = 'Notice for manager';
$string['email:message'] = 'Message';
$string['email:subject'] = 'Subject';
$string['emptylocation'] = 'Location was empty';
$string['enrolled'] = 'enrolled';
$string['error:addalreadysignedupattendee'] = '{$a} is already signed-up for this Face-to-Face activity.';
$string['error:addattendee'] = 'Could not add {$a} to the session.';
$string['error:cancellationsnotallowed'] = 'You are not allowed to cancel this sign-up.';
$string['error:cancelbooking'] = 'There was a problem cancelling your booking';
$string['error:cannotemailmanager'] = 'Sent reminder mail for submission id {$a->submissionid} to user {$a->userid}, but could not send the message for the user\'s manager email address ({$a->manageremail}).';
$string['error:cannotemailuser'] = 'Could not send out mail for submission id {$a->submissionid} to user {$a->userid} ({$a->useremail}).';
$string['error:cannotsendconfirmationmanager'] = 'A confirmation message was sent to your email account, but there was a problem sending the confirmation messsage to your manager\'s email address.';
$string['error:cannotsendconfirmationthirdparty'] = 'A confirmation message was sent to your email account and your manager\'s email account, but there was a problem sending the confirmation messsage to the third party\'s email address.';
$string['error:cannotsendconfirmationuser'] = 'There was a problem sending the confirmation message to your email account.';
$string['error:cannotsendrequestuser'] = 'There was a problem sending the signup request message to your email account.';
$string['error:cannotsendrequestmanager'] = 'There was a problem sending the signup request message to your manager\'s email account.';
$string['error:cannotsendconfirmationusermanager'] = 'A confirmation message could not be sent to your email address and to your manager\'s email address.';
$string['error:canttakeattendanceforunstartedsession'] = 'Can not take attendance for a session that has yet to start.';
$string['error:couldnotaddfield'] = 'Could not add custom session field.';
$string['error:couldnotaddnotice'] = 'Could not add site notice.';
$string['error:couldnotaddsession'] = 'Could not add session';
$string['error:couldnotaddtrainer'] = 'Could not save new Face-to-Face session trainer';
$string['error:couldnotcopysession'] = 'Could not copy session';
$string['error:couldnotdeletefield'] = 'Could not delete custom session field';
$string['error:couldnotdeletenotice'] = 'Could not delete site notice';
$string['error:couldnotdeletesession'] = 'Could not delete session';
$string['error:couldnotdeletetrainer'] = 'Could not delete a Face-to-Face session trainer';
$string['error:couldnotfindsession'] = 'Could not find the newly inserted session';
$string['error:couldnotsavecustomfield'] = 'Could not save custom field';
$string['error:couldnotupdatecalendar'] = 'Could not update session event in the calendar.';
$string['error:couldnotupdatefield'] = 'Could not update custom session field.';
$string['error:couldnotupdatemanageremail'] = 'Could not update manager email address.';
$string['error:couldnotupdatef2frecord'] = 'Could not update Face-to-Face signup record in database';
$string['error:couldnotupdatenotice'] = 'Could not update site notice.';
$string['error:couldnotupdatesession'] = 'Could not update session';
$string['error:coursemisconfigured'] = 'Course is misconfigured';
$string['error:cronprefix'] = 'Error: Face-to-Face cron:';
$string['error:emptymanageremail'] = 'Manager email address empty.';
$string['error:emptylocation'] = 'Location was empty.';
$string['error:emptyvenue'] = 'Venue was empty.';
$string['error:enrolmentfailed'] = 'Could not enrol {$a} into the course.';
$string['error:eventoccurred'] = 'You cannot cancel an event that has already occurred.';
$string['error:fieldidincorrect'] = 'Field ID is incorrect: {$a}';
$string['error:f2ffailedupdatestatus'] = 'Face-to-Face failed to update the user\'s status';
$string['error:incorrectcoursemodule'] = 'Course module is incorrect';
$string['error:incorrectcoursemoduleid'] = 'Course Module ID was incorrect';
$string['error:incorrectcoursemodulesession'] = 'Course Module Face-to-Face Session was incorrect';
$string['error:incorrectfacetofaceid'] = 'Face-to-Face ID was incorrect';
$string['error:incorrectnotificationtype'] = 'Incorrect notification type supplied';
$string['error:invaliduserid'] = 'Invalid user ID';
$string['error:manageremailaddressmissing'] = 'You are currently not assigned to a manager in the system. Please contact the site administrator.';
$string['error:mustspecifycoursemodulefacetoface'] = 'Must specify a course module or a Face-to-Face ID';
$string['error:nomanageremail'] = 'You didn\'t provide an email address for your manager';
$string['error:nomanagersemailset'] = 'No manager email is set';
$string['error:nopermissiontosignup'] = 'You don\'t have permission to signup to this Face-to-Face session.';
$string['error:noticeidincorrect'] = 'Notice ID is incorrect: {$a}';
$string['error:problemsigningup'] = 'There was a problem signing you up.';
$string['error:removeattendee'] = 'Could not remove {$a} from the session.';
$string['error:sessionstartafterend'] = 'Session start date/time is after end.';
$string['error:signedupinothersession'] = 'You are already signed up in another session for this activity. You can only sign up for one session per Face-to-Face activity.';
$string['error:unknownbuttonclicked'] = 'No action associated with the button that was clicked';
$string['excelformat'] = 'Excel';
$string['export'] = 'Export';
$string['exporttofile'] = 'Export to file';
$string['exportattendance'] = 'Export attendance';
$string['facetoface'] = 'Face-to-Face';
$string['facetoface:addinstance'] = 'Add instance';
$string['facetoface:addattendees'] = 'Add attendees to a Face-to-Face session';
$string['facetoface:configurecancellation'] = 'Allow the configuration of sign-up cancellations, upon adding/editing a face-to-face activity.';
$string['facetoface:editsessions'] = 'Add, edit, copy and delete Face-to-Face sessions';
$string['facetoface:overbook'] = 'Sign-up to full sessions.';
$string['facetoface:removeattendees'] = 'Remove attendees from a Face-to-Face session';
$string['facetoface:signup'] = 'Sign-up for a session';
$string['facetoface:takeattendance'] = 'Take attendance';
$string['facetoface:view'] = 'View Face-to-Face activities and sessions';
$string['facetoface:viewattendees'] = 'View attendance list and attendees';
$string['facetoface:viewcancellations'] = 'View cancellations';
$string['facetoface:viewemptyactivities'] = 'View empty Face-to-Face activities';
$string['facetofacebooking'] = 'Face-to-Face booking';
$string['facetofacename'] = 'Face-to-Face name';
$string['facetofacesession'] = 'Face-to-Face session';
$string['feedback'] = 'Feedback';
$string['feedbackupdated'] = 'Feedback updated for \{$a} people';
$string['field:text'] = 'Text';
$string['field:multiselect'] = 'Multiple selection';
$string['field:select'] = 'Menu of choices';
$string['fielddeleteconfirm'] = 'Delete field \'{$a}\' and all session data associated with it?';
$string['floor'] = 'Floor';
$string['format'] = 'Format';
$string['full'] = 'Date is fully occupied';
$string['goback'] = 'Go back';
$string['guestsno'] = 'Sorry, guests are not allowed to sign up for sessions.';
$string['icalendarheading'] = 'iCalendar Attachments';
$string['import'] = 'Import';
$string['info'] = 'Info';
$string['late'] = '\{$a} late';
$string['location'] = 'Location';
$string['lookfor'] = 'Search';
$string['manageradded'] = 'Your manager\'s email address has been accepted.';
$string['managerchanged'] = 'Your manager\'s email address has been changed.';
$string['manageremail'] = 'Manager\'s email';
$string['manageremailaddress'] = 'Manager\'s email address';
$string['manageremailformat'] = 'The email address must be of the format \'{$a}\' to be accepted.';
$string['manageremailheading'] = 'Manager Emails';
$string['manageremailinstruction'] = 'In order to sign-up for a training session, a confirmation email must be sent to your email address and copied to your manager\'s email address.';
$string['manageremailinstructionconfirm'] = 'Please confirm that this is your manager\'s email address:';
$string['managername'] = 'Manager\'s name';
$string['managerupdated'] = 'Your manager\'s email address has been updated.';
$string['maximumpoints'] = 'Maximum number of points';
$string['maximumsize'] = 'Maximum number of attendees';
$string['message'] = 'Change in booking in the course {$a->coursename}!

There has been a free place in the session on {$a->duedate} ({$a->name}) in the course {$a->coursename}.
You have been registered. If the date does not suit you anymore, please unregister at <a href=\'{$a->url}\'>{$a->url}</a>.';
$string['modulename'] = 'Face-to-Face';
$string['modulenameplural'] = 'Face-to-Face';
$string['moreinfo'] = 'More info';
$string['multidate'] = '(multi-date)';
$string['newmanageremailaddress'] = 'Manager\'s email address';
$string['noactionableunapprovedrequests'] = 'No actionable unapproved requests';
$string['nocustomfields'] = '<p>No custom fields are defined.</p>';
$string['nofacetofaces'] = 'There are no Face-to-Face activities';
$string['nositenotices'] = '<p>No site notices are defined.</p>';
$string['none'] = 'none';
$string['normalcost'] = 'Normal cost';
$string['normalcosthinttext'] = '';
$string['noremindersneedtobesent'] = 'No reminders need to be sent.';
$string['nosignedupusers'] = 'No users have signed-up for this session.';
$string['note'] = 'Note';
$string['notefull'] = 'Even if the Session is fully booked you can still register. You will be queued (marked in red). If someone signs out, the first student in the queue will be moved into registeres students and a notification will be sent to him/her by mail.';
$string['notificationtype'] = 'Notification Type';
$string['notificationboth'] = 'Email Notification and iCalendar Appointment';
$string['notificationemail'] = 'Email Notification only';
$string['notificationical'] = 'iCalendar Appointment only';
$string['noticedeleteconfirm'] = 'Delete site notice \'{$a->name}\'?<br/><blockquote>{$a->text}</blockquote>';
$string['noticetext'] = 'Notice text';
$string['notsignedup'] = 'You are not signed up for this session.';
$string['notsubmittedyet'] = 'Not yet evaluated';
$string['noupcoming'] = '<p><i>No upcoming sessions</i></p>';
$string['odsformat'] = 'OpenDocument';
$string['onehour'] = '1 hour';
$string['oneminute'] = '1 minute';
$string['options'] = 'Options';
$string['or'] = 'or';
$string['order'] = 'Order';
$string['place'] = 'Room';
$string['placeholder:facetofacename'] = '[facetofacename]';
$string['placeholder:firstname'] = '[firstname]';
$string['placeholder:lastname'] = '[lastname]';
$string['placeholder:cost'] = '[cost]';
$string['placeholder:alldates'] = '[alldates]';
$string['placeholder:sessiondate'] = '[sessiondate]';
$string['placeholder:starttime'] = '[starttime]';
$string['placeholder:finishtime'] = '[finishtime]';
$string['placeholder:duration'] = '[duration]';
$string['placeholder:details'] = '[details]';
$string['placeholder:reminderperiod'] = '[reminderperiod]';
$string['placeholder:attendeeslink'] = '[attendeeslink]';
$string['pluginadministration'] = 'Face-to-Face administration';
$string['pluginname'] = 'Face-to-Face';
$string['points'] = 'Points';
$string['pointsplural'] = 'Points';
$string['previoussessions'] = 'Previous sessions';
$string['printversionid'] = 'Print version: without name';
$string['printversionname'] = 'Print version: with name';
$string['privacy:metadata:facetoface_signups'] = 'User signups to Face-to-face sessions';
$string['privacy:metadata:facetoface_signups:id'] = 'The id of the signup';
$string['privacy:metadata:facetoface_signups:sessionid'] = 'The session id';
$string['privacy:metadata:facetoface_signups:mailedreminder'] = 'The time a reminder was last sent';
$string['privacy:metadata:facetoface_signups:discountcode'] = 'The discount code entered by the user';
$string['privacy:metadata:facetoface_signups:notificationtype'] = 'How the user would like to be notified.';
$string['privacy:metadata:facetoface_signups_status'] = 'The Status of a user signup to a session';
$string['privacy:metadata:facetoface_signups_status:signupid'] = 'The id of the signup';
$string['privacy:metadata:facetoface_signups_status:statuscode'] = 'The status of the signup eg cancelled,declined,fully_attended';
$string['privacy:metadata:facetoface_signups_status:grade'] = 'The grade assigned for attending the session';
$string['privacy:metadata:facetoface_signups_status:note'] = 'Stores the reason for a cancelled session';
$string['privacy:metadata:facetoface_signups_status:timecreated'] = 'The time the signup was created.';
$string['privacy:metadata:facetoface_session_roles'] = 'Lists users with a trainer role in a facetoface session';
$string['privacy:metadata:userid'] = 'The ID of the user who accessed the Face-to-Face activity';
$string['privacy:metadata:roleid'] = 'The role ID of the user.';
$string['really'] = 'Do you really want to delete all results for this Face-to-Face?';
$string['registeredon'] = 'Registered On';
$string['registrations'] = 'Registrations';
$string['reminder'] = 'Reminder';
$string['remindermessage'] = 'Reminder message';
$string['reminderperiod'] = 'Days before message is sent';
$string['requestmessage'] = 'Request message';
$string['room'] = 'Room';
$string['saveallfeedback'] = 'Save all responses';
$string['saveattendance'] = 'Save attendance';
$string['scheduledsession'] = 'Scheduled session';
$string['scheduledsessions'] = 'Scheduled sessions';
$string['search:activity'] = 'Facetoface activities';
$string['seatsavailable'] = 'Seats available';
$string['seeattendees'] = 'See attendees';
$string['sentremindermanager'] = 'Sent reminder email to user manager';
$string['sentreminderuser'] = 'Sent reminder email to user';
$string['sessiondate'] = 'Session date';
$string['sessiondatetime'] = 'Session date/time';
$string['sessiondatetimeknown'] = 'Session date/time known';
$string['sessionfinishtime'] = 'Session finish time';
$string['sessioninprogress'] = 'session in progress';
$string['sessionisfull'] = 'This session is now full. You will need to pick another time or talk to the instructor.';
$string['sessionover'] = 'session over';
$string['sessions'] = 'Sessions';
$string['sessionsoncoursepage'] = 'Sessions displayed on course page';
$string['sessionstartdateandtime'] = '{$a->startdate}, {$a->starttime} - {$a->endtime} (time zone: {$a->timezone})';
$string['sessionstartdateandtimewithouttimezone'] = '{$a->startdate}, {$a->starttime} - {$a->endtime}';
$string['sessionstartfinishdateandtime'] = '{$a->startdate} - {$a->enddate}, {$a->starttime} - {$a->endtime} (time zone: {$a->timezone})';
$string['sessionstartfinishdateandtimewithouttimezone'] = '{$a->startdate} - {$a->enddate}, {$a->starttime} - {$a->endtime}';
$string['sessionrequiresmanagerapproval'] = 'This session requires manager approval to book.';
$string['sessionroles'] = 'Session roles';
$string['sessionstartdate'] = 'Session start date';
$string['sessionstarttime'] = 'Session start time';
$string['sessionvenue'] = 'Session venue';
$string['setting:addchangemanageremail'] = 'Ask users for their manager\'s email address.';
$string['setting:addchangemanageremaildefault'] = 'Ask users for their manager\'s email address.';
$string['setting:addchangemanageremail_caption'] = 'Manager\'s email:';
$string['setting:attendeesexportfields_caption'] = 'Attendees export fields';
$string['setting:attendeesexportfields'] = 'Select the fields to be included in a session\'s exported list of attendees. This will be in addition to the attendee\'s first and last name.';
$string['setting:defaultcancellationinstrmngr'] = 'Default cancellation message sent to managers.';
$string['setting:defaultcancellationinstrmngr_caption'] = 'Cancellation message (managers)';
$string['setting:defaultcancellationinstrmngrdefault'] = '*** Advice only ****

This is to advise that [firstname] [lastname] is no longer signed-up for the following course and listed you as their Team Leader / Manager.

*** [firstname] [lastname]\'s booking cancellation is copied below ****
';
$string['setting:defaultcancellationmessage'] = 'Default cancellation message sent to the user.';
$string['setting:defaultcancellationmessage_caption'] = 'Cancellation message';
$string['setting:defaultcancellationmessagedefault'] = 'This is to advise that your booking on the following course has been cancelled:

***BOOKING CANCELLED***

Participant:   [firstname] [lastname]
Course:   [facetofacename]

Duration:   [duration]
Date(s):
[alldates]

Location:   [session:location]
Venue:   [session:venue]
Room:   [session:room]
';
$string['setting:defaultcancellationsubject'] = 'Default subject line for cancellation emails.';
$string['setting:defaultcancellationsubject_caption'] = 'Cancellation subject';
$string['setting:defaultcancellationsubjectdefault'] = 'Course booking cancellation';
$string['setting:defaultconfirmationinstrmngr'] = 'Default confirmation message sent to managers.';
$string['setting:defaultconfirmationinstrmngr_caption'] = 'Confirmation message (managers)';
$string['setting:defaultconfirmationinstrmngrdefault'] = '*** Advice only ****

This is to advise that [firstname] [lastname] has been booked for the following course and listed you as their Team Leader / Manager.

If you are not their Team Leader / Manager and believe you have received this email by mistake please reply to this email.  If have concerns about your staff member taking this course please discuss this with them directly.

*** [firstname] [lastname]\'s booking confirmation is copied below ****
';
$string['setting:defaultconfirmationmessage'] = 'Default confirmation message sent to users.';
$string['setting:defaultconfirmationmessage_caption'] = 'Confirmation message';
$string['setting:defaultconfirmationmessagedefault'] = 'This is to confirm that you are now booked on the following course:

Participant:   [firstname] [lastname]
Course:   [facetofacename]
Cost:   [cost]

Duration:    [duration]
Date(s):
[alldates]

Location:   [session:location]
Venue:   [session:venue]
Room:   [session:room]

***Please arrive ten minutes before the course starts***

To re-schedule or cancel your booking
To re-schedule your booking you need to cancel this booking and then re-book a new session.  To cancel your booking, return to the site, then to the page for this course, and then select \'cancel\' from the booking information screen.

[details]

You will receive a reminder [reminderperiod] business days before this course.
';
$string['setting:defaultconfirmationsubject'] = 'Default subject line for confirmation emails.';
$string['setting:defaultconfirmationsubject_caption'] = 'Confirmation subject';
$string['setting:defaultconfirmationsubjectdefault'] = 'Course booking confirmation: [facetofacename], [starttime]-[finishtime], [sessiondate]';
$string['setting:defaultreminderinstrmngr'] = 'Default reminder message sent to managers.';
$string['setting:defaultreminderinstrmngr_caption'] = 'Reminder message (managers)';
$string['setting:defaultreminderinstrmngrdefault'] = '*** Reminder only ****

Your staff member [firstname] [lastname] is booked to attend and above course and has also received this reminder email.

If you are not their Team Leader / Manager and believe you have received this email by mistake please reply to this email.

*** [firstname] [lastname]\'s reminder email is copied below ****
';
$string['setting:defaultremindermessage'] = 'Default reminder message sent to users.';
$string['setting:defaultremindermessage_caption'] = 'Reminder message';
$string['setting:defaultremindermessagedefault'] = 'This is a reminder that you are booked on the following course:

Participant:   [firstname] [lastname]
Course:   [facetofacename]
Cost:   [cost]

Duration:   [duration]
Date(s):
[alldates]

Location:   [session:location]
Venue:   [session:venue]
Room:   [session:room]

***Please arrive ten minutes before the course starts***

To re-schedule or cancel your booking
To re-schedule your booking you need to cancel this booking and then re-book a new session.  To cancel your booking, return to the site, then to the page for this course, and then select \'cancel\' from the booking information screen.

[details]
';
$string['setting:defaultremindersubject'] = 'Default subject line for reminder emails.';
$string['setting:defaultremindersubject_caption'] = 'Reminder subject';
$string['setting:defaultremindersubjectdefault'] = 'Course booking reminder: [facetofacename], [starttime]-[finishtime], [sessiondate]';
$string['setting:defaultrequestinstrmngrdefault'] = 'This is to advise that [firstname] [lastname] has requested to be booked into the following course, and you are listed as their Team Leader / Manager.

Course:   [facetofacename]
Cost:   [cost]

Duration:   [duration]
Date(s):
[alldates]

Location:   [session:location]
Venue:   [session:venue]
Room:   [session:room]

Please follow the link below to approve the request:
[attendeeslink]#unapproved


*** [firstname] [lastname]\'s booking request is copied below ****
';
$string['setting:defaultrequestmessagedefault'] = 'Your request to book into the following course has been sent to your manager:

Participant:   [firstname] [lastname]
Course:   [facetofacename]
Cost:   [cost]

Duration:   [duration]
Date(s):
[alldates]

Location:   [session:location]
Venue:   [session:venue]
Room:   [session:room]
';
$string['setting:defaultrequestsubjectdefault'] = 'Course booking request: [facetofacename], [starttime]-[finishtime]';
$string['setting:defaultvalue'] = 'Default value';
$string['setting:defaultwaitlistedmessage'] = 'Default wait-listed message sent to users.';
$string['setting:defaultwaitlistedmessage_caption'] = 'Wait-listed message';
$string['setting:defaultwaitlistedmessagedefault'] = 'This is to advise that you have been added to the waitlist for:

Course:   [facetofacename]
Location:  [session:location]
Participant:   [firstname] [lastname]

***Please note this is not a course booking confirmation***

By waitlisting you have registered your interest in this course and will be contacted directly when sessions become available.

To remove yourself from this waitlist please return to this course and click Cancel Booking. Please note there is no waitlist removal confirmation email.
';
$string['setting:defaultwaitlistedsubject'] = 'Default subject line for wait-listed emails.';
$string['setting:defaultwaitlistedsubject_caption'] = 'Wait-listed subject';
$string['setting:defaultwaitlistedsubjectdefault'] = 'Waitlisting advice for [facetofacename]';
$string['setting:disableicalcancel'] = 'Disable cancellation emails with an iCalendar attachment.';
$string['setting:disableicalcancel_caption'] = 'Disable iCalendar cancellations:';
$string['setting:fromaddress'] = 'What will appear in the From field of email reminders sent by this module.';
$string['setting:fromaddress_caption'] = 'Sender address:';
$string['setting:fromaddressdefault'] = '<EMAIL>';
$string['setting:manageraddressformat'] = 'Suffix which must be present in the email address of the manager in order to be considered valid.';
$string['setting:manageraddressformat_caption'] = 'Required suffix:';
$string['setting:manageraddressformatdefault'] = '';
$string['setting:manageraddressformatreadable'] = 'Short description of the restrictions on a manager\'s email address.  This setting has no effect if the previous one is not set.';
$string['setting:manageraddressformatreadable_caption'] = 'Format example:';
$string['setting:manageraddressformatreadabledefault'] = '<EMAIL>';
$string['setting:oneemailperday'] = 'Send multiple confirmation emails for multi-day events.';
$string['setting:oneemailperday_caption'] = 'One message per day:';
$string['setting:hidecost'] = 'Hide the cost and discount code fields.';
$string['setting:hidecost_caption'] = 'Hide cost and discount:';
$string['setting:hidediscount'] = 'Hide only the discount code field.';
$string['setting:hidediscount_caption'] = 'Hide discount:';
$string['setting:isfilter'] = 'Display as a filter';
$string['setting:possiblevalues'] = 'List of possible values';
$string['setting:showinsummary'] = 'Show in exports and lists';
$string['setting:sessionroles'] = 'Users assigned to the selected roles in a course can be tracked with each Face-to-Face session';
$string['setting:sessionroles_caption'] = 'Session roles:';
$string['setting:type'] = 'Field type';
$string['showbylocation'] = 'Show by location';
$string['showoncalendar'] = 'Calendar display settings';
$string['signup'] = 'Sign-up';
$string['signups'] = 'Sign-ups';
$string['signupfor'] = 'Sign-up for {$a}';
$string['signupforsession'] = 'Sign-up for an available upcoming session';
$string['signupforthissession'] = 'Sign-up for this Face-to-Face session';
$string['sign-ups'] = 'Sign-ups';
$string['sitenoticesheading'] = 'Site Notices';
$string['subject'] = 'Change in booking in the course {$a->coursename} ({$a->duedate})';
$string['submissions'] = 'Submissions';
$string['submitted'] = 'Submitted';
$string['submit'] = 'Submit';
$string['suppressemail'] = 'Suppress email notification';
$string['status'] = 'Status';
$string['status_booked'] = 'Booked';
$string['status_fully_attended'] = 'Fully attended';
$string['status_no_show'] = 'No show';
$string['status_partially_attended'] = 'Partially attended';
$string['status_requested'] = 'Requested';
$string['status_user_cancelled'] = 'User Cancelled';
$string['status_waitlisted'] = 'Wait-listed';
$string['status_approved'] = 'Approved';
$string['status_declined'] = 'Declined';
$string['status_session_cancelled'] = 'Session Cancelled';
$string['summary'] = 'Summary';
$string['takeattendance'] = 'Take attendance';
$string['time'] = 'Time';
$string['timedue'] = 'Registration deadline';
$string['timefinish'] = 'Finish time';
$string['timestart'] = 'Start time';
$string['timecancelled'] = 'Time Cancelled';
$string['timerequested'] = 'Time Requested';
$string['timesignedup'] = 'Time Signed Up';
$string['datesignedup'] = 'Date Signed Up';
$string['thirdpartyemailaddress'] = 'Third-party email address(es)';
$string['thirdpartywaitlist'] = 'Notify third-party about wait-listed sessions';
$string['unapprovedrequests'] = 'Unapproved Requests';
$string['unknowndate'] = '(unknown date)';
$string['unknowntime'] = '(unknown time)';
$string['upcomingsessions'] = 'Upcoming sessions';
$string['upcomingsessionslist'] = 'List of all upcoming sessions for this Face-to-Face activity';
$string['updaterequests'] = 'Update requests';
$string['upgradeprocessinggrades'] = 'Processing Face-to-Face grades, this may take a while if there are many sessions...';
$string['usercancelledon'] = 'User cancelled on {$a}';
$string['usercalentry'] = 'Show entry on user\'s calendar';
$string['userdeletedcancel'] = 'User has been deleted';
$string['usernotsignedup'] = 'Status: not signed up';
$string['usersignedup'] = 'Status: signed up';
$string['usersignedupon'] = 'User signed up on {$a}';
$string['userwillbewaitlisted'] = 'This session is currently full. By clicking the "Sign-up" button, you will be placed on the sessions\'s wait-list.';
$string['validation:needatleastonedate'] = 'You need to provide at least one date or mark the session as wait-listed.';
$string['venue'] = 'Venue';
$string['viewallsessions'] = 'View all sessions';
$string['viewsubmissions'] = 'View submissions';
$string['waitlistedmessage'] = 'Wait-listed message';
$string['wait-list'] = 'Wait-list';
$string['wait-listed'] = 'Wait-listed';
$string['xhours'] = '{$a} hours';
$string['xminutes'] = '{$a} minutes';
$string['youarebooked'] = 'You are booked for the following session';
$string['youremailaddress'] = 'Your email address';
$string['error:shortnametaken'] = 'Custom field with this short name already exists.';

// Help Text.
$string['allowoverbook_help'] = 'When "Allow overbooking" is checked, learners will be able to sign up for a Face-to-Face session even if it is already full.<br />

When a learner signs up for a session that is already full, they will receive an email advising that they have been waitlisted for the session and will be notified when a booking becomes available.';

$string['approvalreqd_help'] = 'When "Approval required" is checked, a learner will need approval from their manager to be permitted to attend a Face-to-Face session.';

$string['cancellationinstrmngr'] = '# Notice for manager';
$string['cancellationinstrmngr_help'] = 'When **Send notice to manager** is checked, the text in the **Notice for manager** field is sent to a learner\'s manager advising that they have cancelled a Face-to-Face booking.';

$string['cancellationmessage_help'] = 'This message is sent out whenever users cancel their booking for a session.';

$string['capacity_help'] = '**Capacity** is the number of seats available in a session.

When a face-to-Face session reaches capacity the session details do not appear on the course page. The details will appear greyed out on the \'View all sessions\' page and the learner cannot enrol on the session.
&nbsp;';

$string['confirmationinstrmngr'] = '# Notice for manager';
$string['confirmationinstrmngr_help'] = 'When "Send notice to manager" is checked, the text in the "Notice for manager" field is sent to a manager advising that a staff member has signed up for a Face-to-Face session.';

$string['confirmationmessage_help'] = 'This message is sent out whenever users sign up for a session.';

$string['description_help'] = '**Description** is the course description that displays when a learner enrols on a Face-to-Face session.

The **Description** also displays in the training calendar.';

$string['details_help'] = 'Details are tracked per session basis.
If text is populated in the details field, the details text will be displayed on the user signup page.
By default, the details text also appears in the confirmation, reminder, waitlist and cancellation email messages.';
$string['discountcodelearner'] = 'Discount Code';
$string['discountcodelearner_help'] = 'If you know the discount code enter it here. If you leave this field blank you will be charged the normal cost for this event';
$string['discountcode_help'] = 'Discount code is the code required for the discount cost to be tracked for the training of a staff member.
If the staff member does not enter the discount code, the normal cost appears in the training record.';

$string['discountcost_help'] = 'Discount cost is the dollar amount charged to staff members who have a membership id.
If a staff member enters a membership id when signing-up for a session, the discount cost will appear in the cost column in the course\'s gradebook.';

$string['duration_help'] = '**Duration** is the total length of the training in hours.
For example:
* "2 hours" is enters as **2** or **2:00**
* "1 hour and 30 minutes" is entered as **1:30**
* "45 minutes" is entered as **0:45**
* "20 minutes" is entered as **0:20**.

If the training occurs over two or more time periods, the duration is the combined total.';

$string['emailmanagercancellation'] = '# Send notice to manager';
$string['emailmanagercancellation_help'] = 'When "Send notice to manager" is checked, an email will be sent to the learner\'s manager advising them that the Face-to-Face booking has been cancelled.';

$string['emailmanagerconfirmation'] = '# Send notice to manager';
$string['emailmanagerconfirmation_help'] = 'When "Send notice to manager" is checked, a confirmation email will be sent to the learner\'s manager when the learner signs up for a Face-to-Face session.';

$string['emailmanagerreminder'] = '# Send notice to manager';
$string['emailmanagerreminder_help'] = 'When "Send notice to manager" is checked, a reminder message will be sent to the learner\'s manager a few days before the start date of the Face-to-Face session.';

$string['location_help'] = '**Location** describes the vicinity of the session (city, county, region, etc).

**Location** displays on the course page, \'Sign-up page\', the \'View all sessions\' page, and in all email notifications.

On the \'View all sessions\' page, the listed sessions can be filtered by location.';

$string['modulename_help'] = 'The Face-to-Face activity module enables a teacher to set up a voluntary booking system for one or many in-person/classroom based sessions.

Each session within a Face-to-Face activity can have customised settings around location, venue, start time, finish time, cost, capacity, etc. These can be set to run over multiple days or to allow for unscheduled and waitlisted sessions.

An Activity may be set to require manager approval and teachers can configure automated notifications and session reminders for attendees.

Students can view and sign-up for sessions with their attendance tracked and recorded within the Grades area.';

$string['mods_help'] = 'Face-to-Face activities are used to keep track of in-person trainings which require advance booking.

Each activity is offered in one or more identical sessions. These sessions can be given over multiple days.

Reminder messages are sent to users and their managers a few days before the session is scheduled to start. Confirmation messages are sent when users sign-up for a session or cancel.';

$string['normalcost_help'] = 'Normal cost is the dollar amount charged to staff members who do not have a membership id.

The normal cost will appear in the cost column in the course\'s gradebook for each staff member who attends the session (unless a discount cost is entered and the staff member enters a membership id when signing of the session).';

$string['notificationtype_help'] = 'Notification Type allows the learner to select how they would like to be notified of their booking.

* iCalendar appointment only
* Email notification only
* Email notification and iCalendar appointment ';

$string['reminderinstrmngr'] = '# Notice for Manager';
$string['reminderinstrmngr_help'] = 'When **Send notice to manager** is checked, the text in the **Notice for Manager** field is sent to a learner\'s manager advising that they have signed up for a Face-to-Face session.';

$string['remindermessage_help'] = 'This message is sent out a few days before a session\'s start date.';

$string['reminderperiod_help'] = 'The reminder message will be sent this many days before the start of the session.';

$string['requestmessage_help'] = 'When **Approval required** is enabled, the **Request message** section is available.

The **Request message** section displays the notices sent to the learner and their manager regarding the approval process for the learner to attend the Face-to-Face session.

**Subject:** is the subject line that appears on the request approval emails sent to the manager and the learner.

**Message:** is the email text sent to the learner advising them that their request to attend the Face-to-Face session has been sent to their manager for approval.

**Notice for manager:** is the email text sent to the learner\'s manager requesting approval to attend the Face-to-Face session.';

$string['room_help'] = '**Room** is the name/number/identifier of the room being used for the training session.

The **Room** displays on the \'Sign-up\' page, the \'View all sessions\' page and in all email notifications.';

$string['sessiondate'] = 'Session date is the date on which the session occurs.';

$string['sessiondatetimeknown_help'] = '**If a session\'s date/time is known**

If "Yes" is entered for this setting, the session date and time will be displayed on the course page (if the session is upcoming and available), the "View all sessions page", the session sign-up page, as well as all email notifications related to the session.

When a staff member signs up for a session with a known date and time:

* The staff member and the staff member\'s manager will be sent a confirmation email (i.e., the one formatted per the "Confirmation message" section of the Face-to-Face instance\'s settings).
* The staff member will be sent a reminder email message (i.e., the one formatted per the "Reminder message" section of the Face-to-Face instance\'s settings). The reminder will be sent a number of days before the session, according to the "Days before message is sent" setting also found in the "Reminder message" section of the Face-to-Face instance\'s settings.

**If a session\'s date/time is not known (or wait-listed)**

If "No" is entered for this setting, the text "wait-listed" will be displayed on the course page, the "View all sessions page", the session sign-up page, as well as all email notifications related to the session.

When a staff member signs up for a wait-listed session:

* The staff member will be sent a confirmation email (i.e. the one formatted per the "Wait-listed message" section of the Face-to-Face instance\'s settings).
* The staff member will not be sent a reminder email message.
* The staff member\'s manager will not be sent confirmation and cancellation email messages.';

$string['sessionsoncoursepage_help'] = 'This is the number of sessions for each Face-to-Face activity that will be shown on the main course page.';

$string['shortname'] = '# Short Name';
$string['shortname_help'] = '**Short name** is the description of the session that appears on the training calendar when **Show on the calendar** is enabled.';

$string['showoncalendar_help'] = 'When **Site** is selected the Face-to-Face activity sessions will be displayed on the site calendar as a Global Event.  All site users will be able to view these sessions.

When **Course** is selected all of the Face-to-Face activity sessions will be displayed on the course calendar and as Course Event on the site level calendar and visible to all users enrolled in the course.

When **None** is selected, Face-to-Face activity sessions will only be displayed as User Events on a confirmed attendee\'s calendar, provided the **Show on user\'s calendar** option has been selected.';

$string['suppressemail_help'] = 'Use this option if you want to silently add/remove users from a Face-to-Face session. When this option is toggled, the usual email
  confirmation is not sent to the selected users.';

$string['thirdpartyemailaddress_help'] = '**Third-party email address(es)** is an optional field used to specify the email address of a third-party (such as an external instructor) who will then receive confirmation messages whenever a user signs-up for a session.
When entering **multiple email addresses**, separate each address with a comma. For example: <EMAIL>,<EMAIL>';

$string['thirdpartywaitlist_help'] = 'When **Notify third-party about wait-listed sessions** is selected the third-party(s) will be notified when a learner signs up for a wait-listed session. When

**Notify third-party about wait-listed sessions** is not enabled third-party(s) will only be notified when a user signs up (or cancels) for a scheduled session.';

$string['timefinish_help'] = 'Finish time is the time when the session ends.';

$string['timestart_help'] = 'Start time is the time when the session begins.';

$string['usercalentry_help'] = 'When active this setting adds a User Event entry to the calendar of an attendee of a Face-to-Face session. When turned off this prevents a duplicate event appearing in a session attendee\'s calendar, where you have calendar display settings set to Course or Site.';

$string['venue_help'] = '**Venue** is the building the session will be held in.

The **Venue** displays on the \'Sign-up\' page, the \'View all sessions\' page and in all email notifications.';

$string['waitlistedmessage_help'] = 'This message is sent out whenever users sign-up for a wait-listed session.';

/* Face-to-face events and logging */
$string['eventaddsession'] = 'Session added';
$string['eventaddsessionfailed'] = 'Session add (FAILED)';
$string['eventapproverequests'] = 'Session approve requests';
$string['eventattendeesviewed'] = 'Session attendees viewed';
$string['eventattendancetaken'] = 'Session attendance taken';
$string['eventcancelbooking'] = 'Session cancel booking';
$string['eventcancelbookingfailed'] = 'Session cancel booking (FAILED)';
$string['eventcoursef2fviewed'] = 'Course Face-to-Face instances viewed';
$string['eventcoursemoduleviewed'] = 'Face-to-Face module instance viewed';
$string['eventdeletesession'] = 'Session deleted';
$string['eventdeletesessionfailed'] = 'Session delete (FAILED)';
$string['eventsignup'] = 'Session signup';
$string['eventsignupfailed'] = 'Session signup (FAILED)';
$string['eventtakeattendancefailed'] = 'Session take attendance (FAILED)';
$string['eventupdatemanageremailfailed'] = 'Update manager email (FAILED)';
$string['eventupdatesession'] = 'Session updated';
$string['eventupdatesessionfailed'] = 'Session update (FAILED)';
$string['waitliststatus'] = 'You have a place on the waitlist of the following session';


$string['sendstudentinvite'] = 'Send Invitation Link';
$string['sendinvitemessage'] = 'Send Invite Message';
$string['sendinvitemessage_help'] = 'This message is sent to the student as an invitaion to signup for the session.';
$string['setting:defaultsendinvitesubjectdefault'] = 'Booking Open for New Session';
$string['setting:defaultsendinvitemessagedefault'] = '
Hi  [firstname] [lastname],

This is to inform you that the new session has been created and booking are open for the same:

If you are interested you can book your seat by clicking on this link: [link]';

$string['error:couldnotsendinvite'] = 'Could not Sent invite to all users';
$string['placeholder:link'] = '[link]';
$string['savegrades'] = 'Save grades';

$string['pgwaccountidnumber'] = 'Payment account ID Number';
$string['accountidnumber_help'] = 'Payment account ID Number, which will be used to pay the amount of this session';
$string['stripe_not_enabled'] = 'You are adding cost for this session but stripe payment gateway is not enabled and configured. Please configure it and create an account to be used for face-to-face sessions.';
$string['stripe_accountid_notset'] = 'Please configure account ID number before adding cost into a face-to-face session';
$string['payment_method_saved'] = 'Your payment method saved successfully';
$string['payment_method_not_saved'] = 'Your payment method could not be saved. You will be redirected back to this page again';
$string['payment_method_invalid'] = 'Payment method is not valid. You will be redirected back to this page again';

$string['payment_report'] = 'F2F payments report';
$string['payment_failure_reason'] = 'Reason for payments failure';
$string['session_payment_method_required'] = 'This session requires you to submit a payment details which will be charged if you don\'t attend the session. Please save your card details first.';
$string['session_payment_method_removed'] = 'Your payment method for this session has been removed.';
$string['session_payment_method_remove_failed'] = 'Your payment method for this session couldn\'t be removed. Please contact administrator.';

$string['amount_charged'] = 'Amount charged';
$string['retry_payment'] = 'Retry payment';
$string['mark_resolved'] = 'Mark resolved';
$string['export_to_excel'] = 'Export to excel';
$string['export_to_excel_all'] = 'Export all to excel';

// extra strings related to exempt and time based cancellation charge

$string['exempt'] = 'Exempt';
$string['exempt_reason'] = 'Reason for exemption';
$string['percentage_charge'] = 'Percentage charge';
$string['cancellation_slot'] = 'Session cancellation charges slot {$a}';
$string['validation:timelimitfrom'] = 'Starting time {$a} must be set';
$string['validation:timelimitto'] = 'Ending time {$a} must be set';
$string['validation:timelimitto_boundry'] = 'Ending time {$a} must be greater than starting time {$a}';
$string['validation:timelimitfrom_boundry'] = 'Starting time {$a->from} must be greater than ending time {$a->to}';
$string['validation:percentage_charge_sequence'] = 'Please use first slot before using second slot';
$string['session_cancellation_amount'] = 'You will be charged {$a} USD for cancellation.';

// strings for signup and cancellation messages
$string['signup_payment_info'] = 'You are enrolling for an Enphase instructor-led training event offered free of charge to Enphase certified installers. We require a credit card deposit hold to reserve your spot in the class. You will NOT be charged if you attend the class or cancel your reservation before 72 hours of the class date. You must complete online training prior to attending. By providing your credit card information, you acknowledge and agree with these requirements';
$string['session_absent_charge'] = 'You will be charged {$a} USD if you are absent for the session.';
$string['session_cancellation_charge'] = 'You will be charged {$a->amount} USD if you cancel between {$a->fromdays} days and {$a->todays} days from the session start time.';
$string['session_cancellation_charge_boundry'] = 'You will be charged full session amount if you cancel, less than {$a->lowerboundry} days left in session start date and won\'t be charged if more than {$a->upperboundry} days left from session start date.';

//Auto Cancellation String
$string['preqnotification'] = 'Pre-requisite Notification';
$string['autocancellationreminder'] = 'As per the system you have not completed the pre-requisite “{$a}”. We recommend completing the training before the session start date.';
//Enrolment  String
$string['usernotcomplete'] = 'Pre-requisite Notification';
$string['userenrolnotification'] = 'As per the system the user, {$a->username} have not completed the pre-requisite “{$a->lpname}”. The user is now enrolled to the learning plan. Please communicate to the user for the completion of the pre-requisite.';
//new fields string
$string['autoreminder'] = 'Reminder for pre-requisite completion';
$string['autocancellation'] = 'Session auto Cancellation Message';
$string['sessionfull'] = 'The session is full. You can not add new users to the session';
$string['placeholder:pre-requisite'] = '[pre-requisite]';
$string['waitlistnoshow'] = 'Waitlist/No show';
$string['confirmbutton'] = 'Submit to confirm registration';
$string['status_not_applicable'] = 'Not Applicable';
$string['waitliststablesummary'] = 'List of people who are waitlisted in the session.';
$string['facetoface_roaster_email_task'] = 'Roaster emailing task';
$string['facetoface_roaster_email_template'] = '<span dir="ltr">
<p>Hello {$a->trainername},<span style="text-align: var(--bs-body-text-align);">&nbsp;</span></p>
<p>Please see attached roster with participants detail for your upcoming session scheduled on {$a->session_date_time} in {$a->location}. You can add walk in participants by increasing the session seats if already full. Please do remind to walk in installers for completing their pre-requisite in case if not done already.</p>
<p><br></p>
<p>Regards,</p>
<p><span style="text-align: var(--bs-body-text-align);">Enphase University</span></p>
</span>';
$string['facetoface_roaster_email_subject'] = 'Participant Roster for {$a->session_date_time} in {$a->location}';
$string['previoussessionslist'] = 'List of all past sessions for this Face-to-Face activity';

$string['trainer'] = 'Trainer';
$string['sendemailaction'] = 'Send an email to selected attendees';
$string['selectallattendees'] = 'select all attendees to send email';
$string['selectattendee'] = 'select attendee to send email';

$string['manage_custom_fields'] = 'Manage Facetoface Custom Fields';
$string['page:city_add_title'] = 'Add city';
$string['page:city_add_heading'] = 'Add city';
$string['page:city_add_breadcrumb'] = 'Add city';
$string['page:tz_country_add_title'] = 'Add Timezone to Country';
$string['page:tz_country_add_heading'] = 'Add Timezone to Country';
$string['page:tz_country_add_breadcrumb'] = 'Add Timezone to Country';
$string['page:location_add_title'] = 'Add location';
$string['page:location_add_heading'] = 'Add location';
$string['page:location_add_breadcrumb'] = 'Add location';
$string['missing'] = 'Input missing';

$string['autoreminder_help'] = '';
$string['setting:defaultautoremindersubjectdefault'] = '';
$string['setting:defaultautoremindermessagedefault'] = '';
$string['autocancellation_help'] = '';
$string['setting:defaultautocancellationdefault'] = '';
$string['setting:defaultautocancellationsubjectdefault'] = '';
$string['sessionsdetailstablesummary'] = '';
