<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     local_custompage
 * @category    string
 * @copyright   2024 <PERSON> <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['access'] = 'Access';
$string['allauthenticatedusers'] = "All authenticated users";
$string['allauthenticatedusersdesc'] = "All authenticated site users";
$string['allsiteadmin'] = 'Site or tenant administrators';
$string['audience'] = 'Audience';
$string['audiencecreated'] = 'Custom page audience created';
$string['audiencedeleted'] = 'Custom page audience deleted';
$string['audienceupdated'] = 'Custom page audience updated';
$string['audiencewarning'] = 'You need permissions to view or modify this audience';
$string['cachedef_user_allowed_pages'] = 'User allowed pages according to audience';
$string['categoryrole'] = 'Assigned category role';
$string['copyurl'] = 'Copy URL';
$string['custompage:edit'] = 'Edit custom pages';
$string['custompage:editall'] = 'Edit all custom pages';
$string['custompage:view'] = 'View custom pages';
$string['custompage:skipblockvalidation'] = 'Duplicate custom page blocks without validation';
$string['deletepage'] = 'Delete page';
$string['deletepageconfirm'] = 'Are you sure you want to delete the page \'{$a}\'?';
$string['deletepagesuccess'] = 'Deleted page';
$string['duplicatepage'] = 'Duplicate page';
$string['duplicatepageconfirm'] = 'Are you sure you want to duplicate the page \'{$a}\'?';
$string['duplicatepageglobal'] = 'Duplicate to global page';
$string['duplicatepagepostfix'] = '{$a} (copy)';
$string['duplicatepagetenant'] = 'Duplicate to tenant page';
$string['editingpage'] = 'Editing \'{$a}\'';
$string['editname'] = 'Edit name';
$string['editpage'] = 'Edit page';
$string['editpagealert'] = 'Turn edit mode on to add blocks to this page.';
$string['editweight'] = 'Edit weight';
$string['errorpagecreate'] = 'You cannot create new pages';
$string['errorpageedit'] = 'You cannot edit this page';
$string['errorpagelist'] = 'You cannot view the list of pages';
$string['errorpagepreview'] = 'You cannot preview this page';
$string['errorpageview'] = 'You cannot view this page';
$string['globalpage'] = 'Global page';
$string['hidefromnavigation'] = 'Hide from navigation';
$string['myteamspagename'] = 'My teams';
$string['name'] = 'Name';
$string['name_help'] = 'The name will be used as the page heading and as the text in the primary navigation';
$string['newpage'] = 'New page';
$string['newpageglobal'] = 'New global page';
$string['newpagetenant'] = 'New tenant page';
$string['noaudiences'] = 'There are no audiences for this page';
$string['nonauthenticatedusers'] = 'Non-authenticated users';
$string['nonauthenticatedusersdesc'] = 'Guest users and Non-authenticated users';
$string['page'] = 'Page';
$string['pagecreated'] = 'Custom page created';
$string['pagedeleted'] = 'Custom page deleted';
$string['pagetype'] = 'Page type';
$string['pageupdated'] = 'Custom page updated';
$string['pageviewed'] = 'Custom page viewed';
$string['pluginname'] = 'Custom pages';
$string['previewingpage'] = 'Previewing \'{$a}\'';
$string['previewpage'] = 'Preview';
$string['privacy:metadata:audience'] = 'Page audience definitions';
$string['privacy:metadata:audience:classname'] = 'The class used by the audience';
$string['privacy:metadata:audience:configdata'] = 'Configuration data used by the audience';
$string['privacy:metadata:audience:heading'] = '
The custom heading used by the audience';
$string['privacy:metadata:audience:pageid'] = 'The ID of the page the audience belongs to';
$string['privacy:metadata:audience:timecreated'] = 'The time that the audience was created';
$string['privacy:metadata:audience:timemodified'] = 'The time that the audience was last modified';
$string['privacy:metadata:audience:usercreated'] = 'The ID of the user who created the audience';
$string['privacy:metadata:audience:usermodified'] = 'The ID of the user who last modified the audience';
$string['privacy:metadata:page'] = 'Page definitions';
$string['privacy:metadata:page:global'] = 'Whether the page is a global page';
$string['privacy:metadata:page:name'] = 'The name of the page';
$string['privacy:metadata:page:tenantid'] = 'The ID of the tenant the page belongs to';
$string['privacy:metadata:page:timecreated'] = 'The time that the page was created';
$string['privacy:metadata:page:timemodified'] = 'The time that the page was last modified';
$string['privacy:metadata:page:title'] = 'The title of the page';
$string['privacy:metadata:page:usercreated'] = 'The ID of the user who created the page';
$string['privacy:metadata:page:usermodified'] = 'The ID of the user who last modified the page';
$string['privacy:metadata:page:weight'] = 'The weight of the page';
$string['reg_wpcustompages'] = 'Number of custom pages ({$a})';
$string['reg_wpcustompagesglobal'] = 'Number of global custom pages ({$a})';
$string['reg_wpcustompagestenant'] = 'Number of tenant custom pages ({$a})';
$string['showinnavigation'] = 'Show in navigation';
$string['singletenant'] = 'This page is defined as a global page. If this site becomes multi-tenant in the future, this page will be shown to all tenants. Some audience
types such as \'Organisation structure\' are not available here, if you want to use them please duplicate this page';
$string['tenantpage'] = 'Tenant page';
$string['title'] = 'Title in navigation';
$string['title_help'] = 'If specified, the text in the primary navigation can be changed here';
$string['unlisted'] = 'Unlisted';
$string['viewpagealert'] = 'You are editing a custom page. All changes will be visible for those users with permission to access this page.';
$string['visible'] = 'Show in primary navigation';
$string['visible_help'] = 'Show this page in primary navigation. If disabled, users with permission to access the page must do so via a URL.';
$string['weight'] = 'Weight';
$string['weight_help'] = 'Define the order this page will have in primary navigation. Lower numbers will be displayed first';
$string['weightfirst'] = '{$a} (First)';
$string['weightlast'] = '{$a} (Last)';
$string['custompage'] = 'Custom Page';
$string['custompages'] = 'Custom Pages';
$string['managecustompages'] = 'Manage custom pages';
$string['editpagecontent'] = 'Edit custom page';
$string['editpagedetails'] = 'Edit custom page details';
$string['viewpage'] = 'View page';
$string['entitycustompages'] = 'Entity custom pages';
$string['createdby'] = 'Created by';
$string['updatedby'] = 'Updated by';
$string['closeeditor'] = 'Close editor';
$string['content'] = 'Content';
$string['editpagename'] = 'Edit page name';
$string['editpagetitle'] = 'Edit page title';
$string['noblocks'] = 'No blocks in this page';
$string['region-side-pre'] = 'Region right';
$string['region-content'] = 'Region content';
$string['detailssaved'] = 'Page details saved';
