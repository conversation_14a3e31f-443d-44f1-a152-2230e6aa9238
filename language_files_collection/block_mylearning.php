<?php
// This file is part of Moodle - https://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     block_mylearning
 * @category    string
 * @copyright   2024 Krishna <PERSON> <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'My Learning';

$string['allincludinghidden'] = 'All (including removed from view)';
$string['all'] = 'All';
$string['addtofavourites'] = 'Star this course';
$string['aria:addtofavourites'] = 'Star for';
$string['aria:allcoursesincludinghidden'] = 'Show all courses';
$string['aria:allcourses'] = 'Show all courses except courses removed from view';
$string['aria:card'] = 'Switch to card view';
$string['aria:controls'] = 'Course overview controls';
$string['aria:courseactions'] = 'Actions for course';
$string['aria:coursesummary'] = 'Course summary text:';
$string['aria:courseprogress'] = 'Course progress:';
$string['aria:customfield'] = 'Show {$a} courses';
$string['aria:displaydropdown'] = 'Display drop-down menu';
$string['aria:favourites'] = 'Show starred courses only';
$string['aria:future'] = 'Show future courses';
$string['aria:groupingdropdown'] = 'Grouping drop-down menu';
$string['aria:inprogress'] = 'Show courses in progress';
$string['aria:list'] = 'Switch to list view';
$string['aria:past'] = 'Show past courses';
$string['aria:removefromfavourites'] = 'Remove star for';
$string['aria:summary'] = 'Switch to summary view';
$string['aria:sortingdropdown'] = 'Sorting drop-down menu';
$string['availablegroupings'] = 'Available filters';
$string['availablegroupings_desc'] = 'Course filters which are available for selection by users. If none are selected, all courses will be displayed.';
$string['card'] = 'Card';
$string['cards'] = 'Cards';
$string['courseprogress'] = 'Course progress:';
$string['completepercent'] = '{$a}% complete';
$string['customfield'] = 'Custom field';
$string['customfiltergrouping'] = 'Field to use';
$string['customfiltergrouping_nofields'] = 'This option requires a course custom field to be set up and visible to everyone.';
$string['displaycategories'] = 'Display categories';
$string['displaycategories_help'] = 'Display the course category on dashboard course items including cards, list items and summary items.';
$string['favourites'] = 'Starred';
$string['future'] = 'Future';
$string['inprogress'] = 'In progress';
$string['lastaccessed'] = 'Last accessed';
$string['layouts'] = 'Available layouts';
$string['layouts_help'] = 'Course overview layouts which are available for selection by users. If none are selected, the card layout will be used.';
$string['list'] = 'List';
$string['myoverview:myaddinstance'] = 'Add a new course overview block to Dashboard';
$string['nocustomvalue'] = 'No {$a}';
$string['past'] = 'Completed';
$string['privacy:metadata:overviewsortpreference'] = 'The Course overview block sort preference.';
$string['privacy:metadata:overviewviewpreference'] = 'The Course overview block view preference.';
$string['privacy:metadata:overviewgroupingpreference'] = 'The Course overview block grouping preference.';
$string['privacy:metadata:overviewpagingpreference'] = 'The Course overview block paging preference.';
$string['removefromfavourites'] = 'Unstar this course';
$string['searchcourses'] = "Search courses";
$string['shortname'] = 'Short name';
$string['summary'] = 'Summary';
$string['title'] = 'Course name';
$string['aria:hidecourse'] = 'Remove {$a} from view';
$string['aria:showcourse'] = 'Restore {$a} to view';
$string['aria:hiddencourses'] = 'Show courses removed from view';
$string['hidden'] = 'Courses removed from view';
$string['hidecourse'] = 'Remove from view';
$string['hiddencourses'] = 'Removed from view';
$string['show'] = 'Restore to view';
$string['sortbytitle'] = 'Sort by course name';
$string['sortbylastaccessed'] = 'Sort by last accessed';
$string['sortbyshortname'] = 'Sort by short name';
$string['privacy:request:preference:set'] = 'The value of the setting \'{$a->name}\' was \'{$a->value}\'';

$string['sortbyprogramtitle'] = 'Sort by program name';
$string['noprograms'] = 'No programs';
$string['programs'] = 'Programs';

$string['noprogramenrolment'] = '<p>Please <a href="{$a}">enroll</a> for programs to start your training. </p>';
$string['nosacourseenrolment'] = '<p>Please <a href="{$a}">enroll</a> for elective courses to start your training. </p>';

// Deprecated since Moodle 4.0.
$string['clearsearch'] = "Clear search";
$string['aria:lastaccessed'] = 'Sort courses by last accessed date';
$string['aria:shortname'] = 'Sort courses by course short name';
$string['aria:title'] = 'Sort courses by course name';
