<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 *  test2.php description here.
 *
 * @package
 * @copyright  2025 <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use tool_standalonecourses\classification\course\finder;
use block_learning_plan\classification\finder as lp_finder;

require_once(__DIR__ . '/config.php');
require_once($CFG->libdir . '/externallib.php');

echo "fdjgjkdbgkdfsbgk";
$sacs = finder::course_get_standalonecourses_from_search(0, 0, null, null, 12, ['search' => 'test']);
print_object($sacs);
$lps = lp_finder::lp_get_programs_from_search(0, 0, null, 20, ['search' => 'test']);
print_object($lps);

 local_catalog\external::search_items('test');